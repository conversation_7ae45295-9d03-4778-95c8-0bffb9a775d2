package com.weinuo.quickcommands.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.sp
import com.weinuo.quickcommands.R
import com.weinuo.quickcommands.ui.configuration.*
import com.weinuo.quickcommands.data.SettingsRepository
import com.weinuo.quickcommands.ui.theme.manager.ThemeManager
import com.weinuo.quickcommands.ui.components.skyblue.SkyBlueBackButton
import androidx.compose.runtime.CompositionLocalProvider

/**
 * 详细配置界面
 *
 * 全屏配置界面，用于替代原有的配置对话框。
 * 提供更好的用户体验和更大的配置空间。
 * 支持编辑模式，可以预填充现有配置数据进行修改。
 *
 * @param configurationItem 要配置的配置项
 * @param configurationMode 配置模式
 * @param initialConfigObject 初始配置对象（编辑模式使用）
 * @param editIndex 编辑项索引（编辑模式使用）
 * @param onConfigured 配置完成回调，包含配置项和编辑索引
 * @param onDismiss 取消回调
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DetailedConfigurationScreen(
    configurationItem: ConfigurationItem,
    configurationMode: ConfigurationMode,
    initialConfigObject: Any? = null,
    editIndex: Int? = null,
    onConfigured: (Any, Int?) -> Unit,
    onDismiss: () -> Unit
) {
    val context = LocalContext.current

    // 获取主题管理器和设置仓库
    val themeManager = remember { ThemeManager.getInstance(context) }
    val settingsRepository = remember { SettingsRepository(context) }
    val globalSettings by settingsRepository.globalSettings.collectAsState()

    // 主题感知的标题样式
    val themeAwareTitleStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        // 天空蓝主题：使用全局设置的标题字重和字体大小
        MaterialTheme.typography.titleLarge.copy(
            fontWeight = when (globalSettings.topAppBarTitleFontWeight) {
                "normal" -> FontWeight.Normal
                "medium" -> FontWeight.Medium
                "bold" -> FontWeight.Bold
                else -> FontWeight.Medium
            },
            fontSize = globalSettings.screenTitleFontSize.sp
        )
    } else {
        // 海洋蓝主题：保持原有样式
        MaterialTheme.typography.titleLarge
    }

    // 编辑模式检测
    val isEditMode = initialConfigObject != null && editIndex != null

    // 权限检查已在ExpandableConfigurationCard中统一处理，此处不需要重复检查

    // 获取显示信息
    val modeTitle = if (isEditMode) {
        when (configurationMode) {
            ConfigurationMode.TRIGGER_CONDITION -> "编辑条件"
            ConfigurationMode.ABORT_CONDITION -> "编辑中止条件"
            ConfigurationMode.TASK -> "编辑任务"
        }
    } else {
        configurationMode.getDisplayInfo()
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = if (isEditMode) modeTitle else context.getString(R.string.configure_item, configurationItem.title),
                        style = themeAwareTitleStyle
                    )
                },
                navigationIcon = {
                    if (themeManager.getCurrentThemeId() == "sky_blue") {
                        // 天空蓝主题：使用专用的返回按钮
                        SkyBlueBackButton(onClick = onDismiss)
                    } else {
                        // 其他主题：使用原有的箭头图标
                        IconButton(onClick = onDismiss) {
                            Icon(
                                imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                                contentDescription = context.getString(R.string.back)
                            )
                        }
                    }
                }
            )
        }
    ) { paddingValues ->
        // 使用配置项的 configComposable 来渲染配置界面
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            configurationItem.configComposable(
                initialConfigObject,
                { configuredItem ->
                    onConfigured(configuredItem, editIndex)
                },
                onDismiss
            )
        }
    }
}

/**
 * 详细配置内容组件（不依赖NavController）
 *
 * 这是一个纯内容组件，可以在Activity或Fragment中使用，
 * 通过回调函数处理导航逻辑，实现更好的解耦。
 *
 * @param configurationItem 要配置的配置项
 * @param configurationMode 配置模式
 * @param initialConfigObject 初始配置对象（编辑模式使用）
 * @param editIndex 编辑项索引（编辑模式使用）
 * @param onNavigateBack 返回导航回调
 * @param onConfigured 配置完成回调，包含配置项和编辑索引
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DetailedConfigurationContent(
    configurationItem: ConfigurationItem,
    configurationMode: ConfigurationMode,
    initialConfigObject: Any? = null,
    editIndex: Int? = null,
    onNavigateBack: () -> Unit,
    onConfigured: (Any, Int?) -> Unit
) {
    val context = LocalContext.current

    // 获取主题管理器和设置仓库
    val themeManager = remember { ThemeManager.getInstance(context) }
    val settingsRepository = remember { SettingsRepository(context) }
    val globalSettings by settingsRepository.globalSettings.collectAsState()

    // 主题感知的标题样式
    val themeAwareTitleStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        // 天空蓝主题：使用全局设置的标题字重和字体大小
        MaterialTheme.typography.titleLarge.copy(
            fontWeight = when (globalSettings.topAppBarTitleFontWeight) {
                "normal" -> FontWeight.Normal
                "medium" -> FontWeight.Medium
                "bold" -> FontWeight.Bold
                else -> FontWeight.Medium
            },
            fontSize = globalSettings.screenTitleFontSize.sp
        )
    } else {
        // 海洋蓝主题：保持原有样式
        MaterialTheme.typography.titleLarge
    }

    // 编辑模式检测
    val isEditMode = initialConfigObject != null && editIndex != null

    // 权限检查已在ExpandableConfigurationCard中统一处理，此处不需要重复检查

    // 获取显示信息
    val modeTitle = if (isEditMode) {
        when (configurationMode) {
            ConfigurationMode.TRIGGER_CONDITION -> "编辑条件"
            ConfigurationMode.ABORT_CONDITION -> "编辑中止条件"
            ConfigurationMode.TASK -> "编辑任务"
        }
    } else {
        configurationMode.getDisplayInfo()
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = if (isEditMode) modeTitle else context.getString(R.string.configure_item, configurationItem.title),
                        style = themeAwareTitleStyle
                    )
                },
                navigationIcon = {
                    if (themeManager.getCurrentThemeId() == "sky_blue") {
                        // 天空蓝主题：使用专用的返回按钮
                        SkyBlueBackButton(onClick = onNavigateBack)
                    } else {
                        // 其他主题：使用原有的箭头图标
                        IconButton(onClick = onNavigateBack) {
                            Icon(
                                imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                                contentDescription = context.getString(R.string.back)
                            )
                        }
                    }
                }
            )
        }
    ) { paddingValues ->
        // 使用配置项的 configComposable 来渲染配置界面
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // 为configComposable提供一个空的NavController，因为在Activity中不需要导航
            CompositionLocalProvider(LocalNavController provides null) {
                configurationItem.configComposable(
                    initialConfigObject,
                    { configuredItem ->
                        android.util.Log.d("DetailedConfigurationScreen", "Configuration completed: configuredItem=$configuredItem, editIndex=$editIndex")
                        // 配置完成后，调用配置回调（Activity会在回调中处理setResult和finish）
                        onConfigured(configuredItem, editIndex)
                    },
                    onNavigateBack
                )
            }
        }
    }
}
