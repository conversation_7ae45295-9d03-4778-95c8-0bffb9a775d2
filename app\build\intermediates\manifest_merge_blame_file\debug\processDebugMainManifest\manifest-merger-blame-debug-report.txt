1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.weinuo.quickcommands"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="35" />
10
11    <!-- 权限声明 -->
12    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
12-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:6:5-7:53
12-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:6:22-74
13    <uses-permission android:name="android.permission.PACKAGE_USAGE_STATS" />
13-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:8:5-9:47
13-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:8:22-75
14    <uses-permission
14-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:10:5-11:38
15        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
15-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:10:22-78
16        android:maxSdkVersion="29" />
16-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:11:9-35
17    <uses-permission
17-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:12:5-13:38
18        android:name="android.permission.READ_EXTERNAL_STORAGE"
18-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:12:22-77
19        android:maxSdkVersion="32" />
19-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:13:9-35
20    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
20-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:14:5-15:40
20-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:14:22-79
21    <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" />
21-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:16:5-88
21-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:16:22-85
22    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
22-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:17:5-78
22-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:17:22-75
23    <uses-permission android:name="android.permission.WAKE_LOCK" />
23-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:18:5-68
23-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:18:22-65
24    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
24-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:19:5-75
24-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:19:22-72
25    <uses-permission android:name="android.permission.VIBRATE" />
25-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:20:5-66
25-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:20:22-63
26
27    <!-- 音频相关权限 -->
28    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
28-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:23:5-80
28-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:23:22-77
29    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" />
29-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:24:5-85
29-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:24:22-82
30
31    <!-- 网络相关权限 -->
32    <uses-permission android:name="android.permission.INTERNET" />
32-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:27:5-67
32-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:27:22-64
33    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
33-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:28:5-79
33-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:28:22-76
34    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
34-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:29:5-76
34-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:29:22-73
35
36    <!-- 通知权限 (Android 13+) -->
37    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
37-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:32:5-77
37-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:32:22-74
38
39    <!-- 后台进程管理权限 -->
40    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
40-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:35:5-84
40-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:35:22-81
41
42    <!-- 前台服务权限 (Android 9+) -->
43    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
43-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:38:5-77
43-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:38:22-74
44
45    <!-- 前台服务类型权限 (Android 14+) -->
46    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
46-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:41:5-87
46-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:41:22-84
47
48    <!-- 精确闹钟权限 (Android 12+) - 用于时间条件监控 -->
49    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
49-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:44:5-79
49-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:44:22-76
50    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />
50-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:45:5-74
50-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:45:22-71
51
52    <!-- 通信状态条件相关权限 -->
53    <uses-permission android:name="android.permission.READ_CALL_LOG" />
53-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:48:5-72
53-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:48:22-69
54    <uses-permission android:name="android.permission.WRITE_CALL_LOG" />
54-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:49:5-73
54-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:49:22-70
55    <uses-permission android:name="android.permission.CALL_PHONE" />
55-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:50:5-69
55-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:50:22-66
56    <uses-permission android:name="android.permission.MANAGE_OWN_CALLS" />
56-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:51:5-75
56-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:51:22-72
57    <uses-permission android:name="android.permission.READ_SMS" />
57-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:52:5-67
57-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:52:22-64
58    <uses-permission android:name="android.permission.SEND_SMS" />
58-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:53:5-67
58-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:53:22-64
59    <uses-permission android:name="android.permission.READ_CONTACTS" />
59-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:54:5-72
59-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:54:22-69
60
61    <!-- 账号相关权限 -->
62    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
62-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:57:5-71
62-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:57:22-68
63    <uses-permission android:name="android.permission.MANAGE_ACCOUNTS" />
63-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:58:5-74
63-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:58:22-71
64
65    <!-- 传感器状态条件相关权限 -->
66    <uses-permission android:name="android.permission.ACTIVITY_RECOGNITION" />
66-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:61:5-79
66-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:61:22-76
67
68    <!-- 位置相关权限 -->
69    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
69-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:64:5-79
69-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:64:22-76
70    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
70-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:65:5-81
70-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:65:22-78
71
72    <!-- 蓝牙相关权限 -->
73    <uses-permission android:name="android.permission.BLUETOOTH" />
73-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:68:5-68
73-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:68:22-65
74    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
74-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:69:5-74
74-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:69:22-71
75    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
75-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:70:5-76
75-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:70:22-73
76    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
76-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:71:5-73
76-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:71:22-70
77
78    <!-- 设备管理器相关权限 -->
79    <uses-permission android:name="android.permission.BIND_DEVICE_ADMIN" />
79-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:74:5-76
79-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:74:22-73
80
81    <!-- 媒体相关权限 -->
82    <uses-permission android:name="android.permission.RECORD_AUDIO" />
82-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:77:5-71
82-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:77:22-68
83
84    <!-- 相机相关权限 -->
85    <uses-permission android:name="android.permission.CAMERA" />
85-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:80:5-65
85-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:80:22-62
86
87    <!-- 传感器相关的uses-feature声明 -->
88    <uses-feature
88-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:83:5-99
89        android:name="android.hardware.sensor.accelerometer"
89-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:83:19-71
90        android:required="false" />
90-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:83:72-96
91    <uses-feature
91-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:84:5-91
92        android:name="android.hardware.sensor.light"
92-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:84:19-63
93        android:required="false" />
93-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:84:64-88
94    <uses-feature
94-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:85:5-95
95        android:name="android.hardware.sensor.proximity"
95-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:85:19-67
96        android:required="false" />
96-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:85:68-92
97
98    <permission
98-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb17b206e660e96f6900b88209c2dff8\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
99        android:name="com.weinuo.quickcommands.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
99-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb17b206e660e96f6900b88209c2dff8\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
100        android:protectionLevel="signature" />
100-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb17b206e660e96f6900b88209c2dff8\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
101
102    <uses-permission android:name="com.weinuo.quickcommands.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
102-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb17b206e660e96f6900b88209c2dff8\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
102-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb17b206e660e96f6900b88209c2dff8\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
103    <uses-permission android:name="moe.shizuku.manager.permission.API_V23" />
103-->[dev.rikka.shizuku:provider:13.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f8c327978b339642567683fb94fb9dc5\transformed\provider-13.1.5\AndroidManifest.xml:8:5-78
103-->[dev.rikka.shizuku:provider:13.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f8c327978b339642567683fb94fb9dc5\transformed\provider-13.1.5\AndroidManifest.xml:8:22-75
104
105    <application
105-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:87:5-520:19
106        android:allowBackup="true"
106-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:88:9-35
107        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
107-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb17b206e660e96f6900b88209c2dff8\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
108        android:dataExtractionRules="@xml/data_extraction_rules"
108-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:89:9-65
109        android:debuggable="true"
110        android:extractNativeLibs="false"
111        android:fullBackupContent="@xml/backup_rules"
111-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:90:9-54
112        android:icon="@mipmap/ic_launcher"
112-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:91:9-43
113        android:label="@string/app_name"
113-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:92:9-41
114        android:roundIcon="@mipmap/ic_launcher_round"
114-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:93:9-54
115        android:supportsRtl="true"
115-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:94:9-35
116        android:theme="@style/Theme.QuickCommands" >
116-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:95:9-51
117        <activity
117-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:97:9-114:20
118            android:name="com.weinuo.quickcommands.MainActivity"
118-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:98:13-41
119            android:allowEmbedded="true"
119-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:102:13-41
120            android:exported="true"
120-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:99:13-36
121            android:label="@string/app_name"
121-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:100:13-45
122            android:launchMode="singleTask"
122-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:103:13-44
123            android:resizeableActivity="true"
123-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:104:13-46
124            android:theme="@style/Theme.QuickCommands" >
124-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:101:13-55
125            <intent-filter>
125-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:105:13-108:29
126                <action android:name="android.intent.action.MAIN" />
126-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:106:17-69
126-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:106:25-66
127
128                <category android:name="android.intent.category.LAUNCHER" />
128-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:107:17-77
128-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:107:27-74
129            </intent-filter>
130
131            <!-- 声明静态快捷方式 -->
132            <meta-data
132-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:111:13-113:53
133                android:name="android.app.shortcuts"
133-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:112:17-53
134                android:resource="@xml/shortcuts" />
134-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:113:17-50
135        </activity>
136
137        <!-- 气泡通知专用Activity -->
138        <activity
138-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:117:9-126:20
139            android:name="com.weinuo.quickcommands.ui.BubbleActivity"
139-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:118:13-46
140            android:allowEmbedded="true"
140-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:121:13-41
141            android:documentLaunchMode="always"
141-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:122:13-48
142            android:exported="false"
142-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:119:13-37
143            android:launchMode="singleTask"
143-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:125:13-44
144            android:resizeableActivity="true"
144-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:123:13-46
145            android:taskAffinity=""
145-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:124:13-36
146            android:theme="@style/Theme.QuickCommands" >
146-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:120:13-55
147        </activity>
148
149        <!-- 手势录制Activity -->
150        <activity
150-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:129:9-135:20
151            android:name="com.weinuo.quickcommands.ui.recording.GestureRecordingActivity"
151-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:130:13-66
152            android:exported="false"
152-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:131:13-37
153            android:launchMode="singleTask"
153-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:134:13-44
154            android:screenOrientation="portrait"
154-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:133:13-49
155            android:theme="@style/Theme.QuickCommands.NoActionBar" >
155-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:132:13-67
156        </activity>
157
158        <!-- 手势录制编辑Activity -->
159        <activity
159-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:138:9-144:20
160            android:name="com.weinuo.quickcommands.ui.recording.GestureRecordingEditActivity"
160-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:139:13-70
161            android:exported="false"
161-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:140:13-37
162            android:launchMode="singleTask"
162-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:143:13-44
163            android:screenOrientation="portrait"
163-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:142:13-49
164            android:theme="@style/Theme.QuickCommands" >
164-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:141:13-55
165        </activity>
166
167        <!-- 快捷指令执行器活动 -->
168        <activity
168-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:147:9-156:20
169            android:name="com.weinuo.quickcommands.shortcut.QuickCommandExecutorActivity"
169-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:148:13-66
170            android:exported="true"
170-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:149:13-36
171            android:launchMode="singleTask"
171-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:151:13-44
172            android:theme="@style/Theme.QuickCommands.NoActionBar" >
172-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:150:13-67
173            <intent-filter>
173-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:152:13-155:29
174                <action android:name="android.intent.action.VIEW" />
174-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:153:17-69
174-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:153:25-66
175
176                <category android:name="android.intent.category.DEFAULT" />
176-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:154:17-76
176-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:154:27-73
177            </intent-filter>
178        </activity>
179
180        <!-- 静态快捷方式处理活动 -->
181        <activity
181-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:159:9-168:20
182            android:name="com.weinuo.quickcommands.shortcut.StaticShortcutHandlerActivity"
182-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:160:13-67
183            android:exported="true"
183-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:161:13-36
184            android:launchMode="singleTask"
184-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:163:13-44
185            android:theme="@style/Theme.QuickCommands.NoActionBar" >
185-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:162:13-67
186            <intent-filter>
186-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:152:13-155:29
187                <action android:name="android.intent.action.VIEW" />
187-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:153:17-69
187-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:153:25-66
188
189                <category android:name="android.intent.category.DEFAULT" />
189-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:154:17-76
189-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:154:27-73
190            </intent-filter>
191        </activity>
192
193        <!-- 桌面小组件点击处理活动 -->
194        <activity
194-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:171:9-180:20
195            android:name="com.weinuo.quickcommands.widget.WidgetClickHandlerActivity"
195-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:172:13-62
196            android:exported="true"
196-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:173:13-36
197            android:launchMode="singleTask"
197-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:175:13-44
198            android:theme="@style/Theme.QuickCommands.NoActionBar" >
198-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:174:13-67
199            <intent-filter>
199-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:152:13-155:29
200                <action android:name="android.intent.action.VIEW" />
200-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:153:17-69
200-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:153:25-66
201
202                <category android:name="android.intent.category.DEFAULT" />
202-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:154:17-76
202-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:154:27-73
203            </intent-filter>
204        </activity>
205
206        <!-- 快捷指令表单Activity -->
207        <activity
207-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:183:9-188:20
208            android:name="com.weinuo.quickcommands.ui.activities.QuickCommandFormActivity"
208-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:184:13-67
209            android:exported="false"
209-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:185:13-37
210            android:launchMode="singleTask"
210-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:187:13-44
211            android:theme="@style/Theme.QuickCommands" >
211-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:186:13-55
212        </activity>
213
214        <!-- 图标选择Activity -->
215        <activity
215-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:191:9-196:20
216            android:name="com.weinuo.quickcommands.ui.activities.IconSelectionActivity"
216-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:192:13-64
217            android:exported="false"
217-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:193:13-37
218            android:launchMode="singleTask"
218-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:195:13-44
219            android:theme="@style/Theme.QuickCommands" >
219-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:194:13-55
220        </activity>
221
222        <!-- 应用选择Activity -->
223        <activity
223-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:199:9-204:20
224            android:name="com.weinuo.quickcommands.ui.activities.AppSelectionActivity"
224-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:200:13-63
225            android:exported="false"
225-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:201:13-37
226            android:launchMode="singleTask"
226-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:203:13-44
227            android:theme="@style/Theme.QuickCommands" >
227-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:202:13-55
228        </activity>
229
230        <!-- 统一配置Activity -->
231        <activity
231-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:207:9-212:20
232            android:name="com.weinuo.quickcommands.ui.activities.UnifiedConfigurationActivity"
232-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:208:13-71
233            android:exported="false"
233-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:209:13-37
234            android:launchMode="singleTask"
234-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:211:13-44
235            android:theme="@style/Theme.QuickCommands" >
235-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:210:13-55
236        </activity>
237
238        <!-- 详细配置Activity -->
239        <activity
239-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:215:9-220:20
240            android:name="com.weinuo.quickcommands.ui.activities.DetailedConfigurationActivity"
240-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:216:13-72
241            android:exported="false"
241-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:217:13-37
242            android:launchMode="singleTask"
242-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:219:13-44
243            android:theme="@style/Theme.QuickCommands" >
243-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:218:13-55
244        </activity>
245
246        <!-- 单个配置项配置Activity -->
247        <activity
247-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:223:9-228:20
248            android:name="com.weinuo.quickcommands.ui.activities.ItemConfigurationActivity"
248-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:224:13-68
249            android:exported="false"
249-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:225:13-37
250            android:launchMode="singleTask"
250-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:227:13-44
251            android:theme="@style/Theme.QuickCommands" >
251-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:226:13-55
252        </activity>
253
254        <!-- 联系人选择Activity -->
255        <activity
255-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:231:9-236:20
256            android:name="com.weinuo.quickcommands.ui.activities.ContactSelectionActivity"
256-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:232:13-67
257            android:exported="false"
257-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:233:13-37
258            android:launchMode="singleTask"
258-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:235:13-44
259            android:theme="@style/Theme.QuickCommands" >
259-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:234:13-55
260        </activity>
261
262        <!-- 铃声选择Activity -->
263        <activity
263-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:239:9-244:20
264            android:name="com.weinuo.quickcommands.ui.activities.RingtoneSelectionActivity"
264-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:240:13-68
265            android:exported="false"
265-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:241:13-37
266            android:launchMode="singleTask"
266-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:243:13-44
267            android:theme="@style/Theme.QuickCommands" >
267-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:242:13-55
268        </activity>
269
270        <!-- 分享目标选择Activity -->
271        <activity
271-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:247:9-252:20
272            android:name="com.weinuo.quickcommands.ui.activities.ShareTargetSelectionActivity"
272-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:248:13-71
273            android:exported="false"
273-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:249:13-37
274            android:launchMode="singleTask"
274-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:251:13-44
275            android:theme="@style/Theme.QuickCommands" >
275-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:250:13-55
276        </activity>
277
278        <!-- 高级内存配置Activity -->
279        <activity
279-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:255:9-260:20
280            android:name="com.weinuo.quickcommands.ui.activities.AdvancedMemoryConfigActivity"
280-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:256:13-71
281            android:exported="false"
281-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:257:13-37
282            android:launchMode="singleTask"
282-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:259:13-44
283            android:theme="@style/Theme.QuickCommands" >
283-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:258:13-55
284        </activity>
285
286        <!-- 内存学习数据Activity -->
287        <activity
287-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:263:9-268:20
288            android:name="com.weinuo.quickcommands.ui.activities.MemoryLearningDataActivity"
288-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:264:13-69
289            android:exported="false"
289-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:265:13-37
290            android:launchMode="singleTask"
290-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:267:13-44
291            android:theme="@style/Theme.QuickCommands" >
291-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:266:13-55
292        </activity>
293
294        <!-- 高级清理策略配置Activity -->
295        <activity
295-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:271:9-276:20
296            android:name="com.weinuo.quickcommands.ui.activities.AdvancedCleanupStrategyActivity"
296-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:272:13-74
297            android:exported="false"
297-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:273:13-37
298            android:launchMode="singleTask"
298-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:275:13-44
299            android:theme="@style/Theme.QuickCommands" >
299-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:274:13-55
300        </activity>
301
302        <!-- 添加清理规则Activity -->
303        <activity
303-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:279:9-284:20
304            android:name="com.weinuo.quickcommands.ui.activities.AddCleanupRuleActivity"
304-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:280:13-65
305            android:exported="false"
305-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:281:13-37
306            android:launchMode="singleTask"
306-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:283:13-44
307            android:theme="@style/Theme.QuickCommands" >
307-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:282:13-55
308        </activity>
309
310        <!-- 智慧提醒详细配置Activity -->
311        <activity
311-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:287:9-292:20
312            android:name="com.weinuo.quickcommands.ui.activities.SmartReminderDetailConfigActivity"
312-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:288:13-76
313            android:exported="false"
313-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:289:13-37
314            android:launchMode="singleTask"
314-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:291:13-44
315            android:theme="@style/Theme.QuickCommands" >
315-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:290:13-55
316        </activity>
317
318        <!-- 桌面小组件1 -->
319        <receiver
319-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:295:9-305:20
320            android:name="com.weinuo.quickcommands.widget.OneClickCommandWidget1"
320-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:296:13-58
321            android:exported="true"
321-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:297:13-36
322            android:label="@string/widget_1_label" >
322-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:298:13-51
323            <intent-filter>
323-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:299:13-301:29
324                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
324-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:300:17-84
324-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:300:25-81
325            </intent-filter>
326
327            <meta-data
327-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:302:13-304:75
328                android:name="android.appwidget.provider"
328-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:303:17-58
329                android:resource="@xml/one_click_command_widget_1_info" />
329-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:304:17-72
330        </receiver>
331
332        <!-- 桌面小组件2 -->
333        <receiver
333-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:308:9-318:20
334            android:name="com.weinuo.quickcommands.widget.OneClickCommandWidget2"
334-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:309:13-58
335            android:exported="true"
335-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:310:13-36
336            android:label="@string/widget_2_label" >
336-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:311:13-51
337            <intent-filter>
337-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:299:13-301:29
338                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
338-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:300:17-84
338-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:300:25-81
339            </intent-filter>
340
341            <meta-data
341-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:302:13-304:75
342                android:name="android.appwidget.provider"
342-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:303:17-58
343                android:resource="@xml/one_click_command_widget_2_info" />
343-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:304:17-72
344        </receiver>
345
346        <!-- 桌面小组件3 -->
347        <receiver
347-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:321:9-331:20
348            android:name="com.weinuo.quickcommands.widget.OneClickCommandWidget3"
348-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:322:13-58
349            android:exported="true"
349-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:323:13-36
350            android:label="@string/widget_3_label" >
350-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:324:13-51
351            <intent-filter>
351-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:299:13-301:29
352                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
352-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:300:17-84
352-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:300:25-81
353            </intent-filter>
354
355            <meta-data
355-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:302:13-304:75
356                android:name="android.appwidget.provider"
356-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:303:17-58
357                android:resource="@xml/one_click_command_widget_3_info" />
357-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:304:17-72
358        </receiver>
359
360        <!-- 桌面小组件4 -->
361        <receiver
361-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:334:9-344:20
362            android:name="com.weinuo.quickcommands.widget.OneClickCommandWidget4"
362-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:335:13-58
363            android:exported="true"
363-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:336:13-36
364            android:label="@string/widget_4_label" >
364-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:337:13-51
365            <intent-filter>
365-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:299:13-301:29
366                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
366-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:300:17-84
366-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:300:25-81
367            </intent-filter>
368
369            <meta-data
369-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:302:13-304:75
370                android:name="android.appwidget.provider"
370-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:303:17-58
371                android:resource="@xml/one_click_command_widget_4_info" />
371-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:304:17-72
372        </receiver>
373
374        <!-- 设备管理器接收器 -->
375        <receiver
375-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:347:9-355:20
376            android:name="com.weinuo.quickcommands.permission.DeviceAdminReceiver"
376-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:347:19-65
377            android:exported="true"
377-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:349:13-36
378            android:permission="android.permission.BIND_DEVICE_ADMIN" >
378-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:348:13-70
379            <meta-data
379-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:350:13-351:56
380                android:name="android.app.device_admin"
380-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:350:24-63
381                android:resource="@xml/device_admin" />
381-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:351:17-53
382
383            <intent-filter>
383-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:352:13-354:29
384                <action android:name="android.app.action.DEVICE_ADMIN_ENABLED" />
384-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:353:17-82
384-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:353:25-79
385            </intent-filter>
386        </receiver>
387
388        <!-- Shizuku UserService -->
389        <provider
390            android:name="rikka.shizuku.ShizukuProvider"
390-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:359:13-57
391            android:authorities="com.weinuo.quickcommands.shizuku"
391-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:360:13-59
392            android:enabled="true"
392-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:361:13-35
393            android:exported="true"
393-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:362:13-36
394            android:multiprocess="false"
394-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:363:13-41
395            android:permission="android.permission.INTERACT_ACROSS_USERS_FULL" />
395-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:364:13-79
396
397        <!-- 快捷指令服务 -->
398        <service
398-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:367:9-371:56
399            android:name="com.weinuo.quickcommands.service.QuickCommandsService"
399-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:368:13-57
400            android:enabled="true"
400-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:369:13-35
401            android:exported="false"
401-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:370:13-37
402            android:foregroundServiceType="dataSync" />
402-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:371:13-53
403
404        <!-- 闹钟悬浮窗服务 -->
405        <service
405-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:376:9-379:40
406            android:name="com.weinuo.quickcommands.service.AlarmOverlayService"
406-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:377:13-56
407            android:enabled="true"
407-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:378:13-35
408            android:exported="false" />
408-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:379:13-37
409
410        <!-- 触摸屏蔽悬浮窗服务 -->
411        <service
411-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:382:9-385:40
412            android:name="com.weinuo.quickcommands.service.TouchBlockOverlayService"
412-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:383:13-61
413            android:enabled="true"
413-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:384:13-35
414            android:exported="false" />
414-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:385:13-37
415
416        <!-- 剪贴板刷新叠加层服务 -->
417        <service
417-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:388:9-391:40
418            android:name="com.weinuo.quickcommands.service.ClipboardRefreshOverlayService"
418-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:389:13-67
419            android:enabled="true"
419-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:390:13-35
420            android:exported="false" />
420-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:391:13-37
421
422        <!-- 悬浮按钮服务 -->
423        <service
423-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:394:9-397:40
424            android:name="com.weinuo.quickcommands.service.FloatingButtonService"
424-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:395:13-58
425            android:enabled="true"
425-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:396:13-35
426            android:exported="false" />
426-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:397:13-37
427
428        <!-- 智慧提醒悬浮窗服务 -->
429        <service
429-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:400:9-403:40
430            android:name="com.weinuo.quickcommands.service.SmartReminderOverlayService"
430-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:401:13-64
431            android:enabled="true"
431-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:402:13-35
432            android:exported="false" />
432-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:403:13-37
433
434        <!-- 悬浮录制服务 -->
435        <service
435-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:406:9-409:40
436            android:name="com.weinuo.quickcommands.floating.FloatingRecordingService"
436-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:407:13-62
437            android:enabled="true"
437-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:408:13-35
438            android:exported="false" />
438-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:409:13-37
439
440        <!-- 悬浮加速球服务 -->
441        <service
441-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:412:9-415:40
442            android:name="com.weinuo.quickcommands.floating.FloatingAcceleratorService"
442-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:413:13-64
443            android:enabled="true"
443-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:414:13-35
444            android:exported="false" />
444-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:415:13-37
445
446        <!-- 高级悬浮录制服务 -->
447        <service
447-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:418:9-421:40
448            android:name="com.weinuo.quickcommands.floating.AdvancedFloatingRecordingService"
448-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:419:13-70
449            android:enabled="true"
449-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:420:13-35
450            android:exported="false" />
450-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:421:13-37
451
452        <!-- 快捷指令-系统优先级提升无障碍服务 -->
453        <service
453-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:424:9-436:19
454            android:name="com.weinuo.quickcommands.service.SystemPriorityEnhancementService"
454-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:425:13-69
455            android:description="@string/accessibility_service_system_description"
455-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:427:13-83
456            android:exported="true"
456-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:429:13-36
457            android:label="@string/accessibility_service_name"
457-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:426:13-63
458            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
458-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:428:13-79
459            <intent-filter>
459-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:430:13-432:29
460                <action android:name="android.accessibilityservice.AccessibilityService" />
460-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:431:17-92
460-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:431:25-89
461            </intent-filter>
462
463            <meta-data
463-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:433:13-435:72
464                android:name="android.accessibilityservice"
464-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:434:17-60
465                android:resource="@xml/accessibility_service_config" />
465-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:435:17-69
466        </service>
467
468        <!-- 快捷指令-系统操作无障碍服务 -->
469        <service
469-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:439:9-451:19
470            android:name="com.weinuo.quickcommands.service.SystemOperationAccessibilityService"
470-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:440:13-72
471            android:description="@string/system_operation_accessibility_service_description"
471-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:442:13-93
472            android:exported="true"
472-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:444:13-36
473            android:label="@string/system_operation_accessibility_service_name"
473-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:441:13-80
474            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
474-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:443:13-79
475            <intent-filter>
475-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:430:13-432:29
476                <action android:name="android.accessibilityservice.AccessibilityService" />
476-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:431:17-92
476-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:431:25-89
477            </intent-filter>
478
479            <meta-data
479-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:433:13-435:72
480                android:name="android.accessibilityservice"
480-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:434:17-60
481                android:resource="@xml/system_operation_accessibility_service_config" />
481-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:435:17-69
482        </service>
483
484        <!-- 快捷指令-界面交互服务 -->
485        <service
485-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:454:9-466:19
486            android:name="com.weinuo.quickcommands.service.InterfaceInteractionAccessibilityService"
486-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:455:13-77
487            android:description="@string/interface_interaction_accessibility_service_description"
487-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:457:13-98
488            android:exported="true"
488-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:459:13-36
489            android:label="@string/interface_interaction_accessibility_service_name"
489-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:456:13-85
490            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
490-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:458:13-79
491            <intent-filter>
491-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:430:13-432:29
492                <action android:name="android.accessibilityservice.AccessibilityService" />
492-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:431:17-92
492-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:431:25-89
493            </intent-filter>
494
495            <meta-data
495-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:433:13-435:72
496                android:name="android.accessibilityservice"
496-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:434:17-60
497                android:resource="@xml/interface_interaction_accessibility_service_config" />
497-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:435:17-69
498        </service>
499
500        <!-- 快捷指令-手势识别服务 -->
501        <service
501-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:469:9-481:19
502            android:name="com.weinuo.quickcommands.service.GestureRecognitionAccessibilityService"
502-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:470:13-75
503            android:description="@string/gesture_recognition_accessibility_service_description"
503-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:472:13-96
504            android:exported="true"
504-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:474:13-36
505            android:label="@string/gesture_recognition_accessibility_service_name"
505-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:471:13-83
506            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
506-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:473:13-79
507            <intent-filter>
507-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:430:13-432:29
508                <action android:name="android.accessibilityservice.AccessibilityService" />
508-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:431:17-92
508-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:431:25-89
509            </intent-filter>
510
511            <meta-data
511-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:433:13-435:72
512                android:name="android.accessibilityservice"
512-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:434:17-60
513                android:resource="@xml/gesture_recognition_accessibility_service_config" />
513-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:435:17-69
514        </service>
515
516        <!-- 快捷指令-自动点击器服务 -->
517        <service
517-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:484:9-496:19
518            android:name="com.weinuo.quickcommands.service.AutoClickerAccessibilityService"
518-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:485:13-68
519            android:description="@string/auto_clicker_accessibility_service_description"
519-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:487:13-89
520            android:exported="true"
520-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:489:13-36
521            android:label="@string/auto_clicker_accessibility_service_name"
521-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:486:13-76
522            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
522-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:488:13-79
523            <intent-filter>
523-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:430:13-432:29
524                <action android:name="android.accessibilityservice.AccessibilityService" />
524-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:431:17-92
524-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:431:25-89
525            </intent-filter>
526
527            <meta-data
527-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:433:13-435:72
528                android:name="android.accessibilityservice"
528-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:434:17-60
529                android:resource="@xml/auto_clicker_accessibility_service_config" />
529-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:435:17-69
530        </service>
531
532        <!-- 通知监听服务 -->
533        <service
533-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:499:9-508:19
534            android:name="com.weinuo.quickcommands.service.QuickCommandsNotificationService"
534-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:500:13-69
535            android:description="@string/notification_listener_service_description"
535-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:502:13-84
536            android:exported="true"
536-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:504:13-36
537            android:label="@string/notification_listener_service_name"
537-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:501:13-71
538            android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" >
538-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:503:13-87
539            <intent-filter>
539-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:505:13-507:29
540                <action android:name="android.service.notification.NotificationListenerService" />
540-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:506:17-99
540-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:506:25-96
541            </intent-filter>
542        </service>
543
544        <!-- FileProvider for camera file sharing -->
545        <provider
546            android:name="androidx.core.content.FileProvider"
546-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:512:13-62
547            android:authorities="com.weinuo.quickcommands.fileprovider"
547-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:513:13-64
548            android:exported="false"
548-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:514:13-37
549            android:grantUriPermissions="true" >
549-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:515:13-47
550            <meta-data
550-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:516:13-518:63
551                android:name="android.support.FILE_PROVIDER_PATHS"
551-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:517:17-67
552                android:resource="@xml/file_provider_paths" />
552-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:518:17-60
553        </provider>
554        <provider
554-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d27b9367432edd9869b9297aaff418e3\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
555            android:name="androidx.startup.InitializationProvider"
555-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d27b9367432edd9869b9297aaff418e3\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
556            android:authorities="com.weinuo.quickcommands.androidx-startup"
556-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d27b9367432edd9869b9297aaff418e3\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
557            android:exported="false" >
557-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d27b9367432edd9869b9297aaff418e3\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
558            <meta-data
558-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d27b9367432edd9869b9297aaff418e3\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
559                android:name="androidx.emoji2.text.EmojiCompatInitializer"
559-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d27b9367432edd9869b9297aaff418e3\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
560                android:value="androidx.startup" />
560-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d27b9367432edd9869b9297aaff418e3\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
561            <meta-data
561-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae32d35540cb17a90796eacb79dc150b\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
562                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
562-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae32d35540cb17a90796eacb79dc150b\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
563                android:value="androidx.startup" />
563-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae32d35540cb17a90796eacb79dc150b\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
564            <meta-data
564-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
565                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
565-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
566                android:value="androidx.startup" />
566-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
567        </provider>
568
569        <receiver
569-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
570            android:name="androidx.profileinstaller.ProfileInstallReceiver"
570-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
571            android:directBootAware="false"
571-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
572            android:enabled="true"
572-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
573            android:exported="true"
573-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
574            android:permission="android.permission.DUMP" >
574-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
575            <intent-filter>
575-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
576                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
576-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
576-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
577            </intent-filter>
578            <intent-filter>
578-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
579                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
579-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
579-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
580            </intent-filter>
581            <intent-filter>
581-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
582                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
582-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
582-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
583            </intent-filter>
584            <intent-filter>
584-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
585                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
585-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
585-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
586            </intent-filter>
587        </receiver>
588
589        <meta-data
589-->[dev.rikka.shizuku:provider:13.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f8c327978b339642567683fb94fb9dc5\transformed\provider-13.1.5\AndroidManifest.xml:11:9-13:36
590            android:name="moe.shizuku.client.V3_SUPPORT"
590-->[dev.rikka.shizuku:provider:13.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f8c327978b339642567683fb94fb9dc5\transformed\provider-13.1.5\AndroidManifest.xml:12:13-57
591            android:value="true" />
591-->[dev.rikka.shizuku:provider:13.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f8c327978b339642567683fb94fb9dc5\transformed\provider-13.1.5\AndroidManifest.xml:13:13-33
592    </application>
593
594</manifest>
