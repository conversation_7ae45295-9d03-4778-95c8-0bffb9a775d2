# 配置保存问题修复计划

## 问题分析

### 现有项目与old项目的差异

#### 1. 数据传递机制差异
**old项目**：
- 使用ActivityResultLauncher处理配置Activity的返回结果
- 配置完成后通过NavigationDataStorageManager保存数据
- 返回navigationKey给调用方
- 调用方通过navigationKey加载配置数据

**现有项目**：
- ItemConfigurationActivity.startForConfiguration()直接启动Activity，没有返回结果处理
- MainActivity中没有ActivityResultLauncher来接收ItemConfigurationActivity的结果
- 配置完成后数据无法传递回调用方

#### 2. 调用方式差异
**old项目**：
```kotlin
// UnifiedConfigurationActivity中
private val detailedConfigurationLauncher = registerForActivityResult(
    ActivityResultContracts.StartActivityForResult()
) { result ->
    if (result.resultCode == Activity.RESULT_OK) {
        val navigationKey = result.data?.getStringExtra("navigation_key")
        // 处理配置结果
    }
}
```

**现有项目**：
```kotlin
// NonExpandableConfigurationCard中
ItemConfigurationActivity.startForConfiguration(
    context = context,
    itemId = item.id,
    itemTitle = item.title,
    initialConfig = initialConfigObject
)
// 没有结果处理机制
```

## 修复方案

### 方案1：为NonExpandableConfigurationCard添加ActivityResultLauncher

#### 步骤1：修改NonExpandableConfigurationCard
- 添加ActivityResultLauncher参数
- 使用launcher.launch()替代直接startActivity()

#### 步骤2：修改UniversalDetailConfigurationScreen
- 创建ActivityResultLauncher
- 传递给NonExpandableConfigurationCard

#### 步骤3：修改MainActivity
- 在导航到UniversalDetailConfigurationScreen时处理配置结果

### 方案2：修改ItemConfigurationActivity的启动方式

#### 步骤1：添加ActivityResultLauncher支持
```kotlin
companion object {
    fun startForConfigurationWithResult(
        launcher: ActivityResultLauncher<Intent>,
        context: Context,
        itemId: String,
        itemTitle: String,
        initialConfig: Any? = null,
        editIndex: Int? = null
    ) {
        val intent = Intent(context, ItemConfigurationActivity::class.java).apply {
            putExtra(EXTRA_ITEM_ID, itemId)
            putExtra(EXTRA_ITEM_TITLE, itemTitle)
            initialConfig?.let { putExtra(EXTRA_INITIAL_CONFIG, it.toString()) }
            editIndex?.let { putExtra(EXTRA_EDIT_INDEX, it) }
        }
        launcher.launch(intent)
    }
}
```

#### 步骤2：修改调用方
- 在UniversalDetailConfigurationScreen中创建launcher
- 使用新的启动方式

## 推荐方案

采用**方案2**，因为：
1. 保持与old项目的一致性
2. 最小化代码修改
3. 复用现有的NavigationDataStorageManager机制

## 实施步骤

### 第一步：修改ItemConfigurationActivity
1. 添加startForConfigurationWithResult方法
2. 确保配置完成后正确返回navigationKey

### 第二步：修改UniversalDetailConfigurationScreen
1. 添加ActivityResultLauncher
2. 修改NonExpandableConfigurationCard的调用方式
3. 处理配置完成的结果

### 第三步：修改MainActivity
1. 在导航处理中接收配置结果
2. 更新快捷指令数据

### 第四步：测试验证
1. 测试传感器状态配置
2. 测试应用状态配置
3. 测试设备事件配置
4. 验证配置数据正确保存

## 关键代码位置

1. `app/src/main/java/com/weinuo/quickcommands/ui/activities/ItemConfigurationActivity.kt`
2. `app/src/main/java/com/weinuo/quickcommands/ui/screens/UniversalDetailConfigurationScreen.kt`
3. `app/src/main/java/com/weinuo/quickcommands/ui/components/NonExpandableConfigurationCard.kt`
4. `app/src/main/java/com/weinuo/quickcommands/MainActivity.kt`
