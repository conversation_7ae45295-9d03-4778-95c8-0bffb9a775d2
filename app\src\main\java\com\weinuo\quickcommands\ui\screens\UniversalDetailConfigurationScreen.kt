package com.weinuo.quickcommands.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import com.weinuo.quickcommands.R
import com.weinuo.quickcommands.ui.components.ConfigurationCardItem
import com.weinuo.quickcommands.ui.components.NonExpandableConfigurationCard
import com.weinuo.quickcommands.ui.components.ExpandableConfigurationCard
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.setValue
import com.weinuo.quickcommands.ui.screens.LocalNavController
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.runtime.CompositionLocalProvider
import android.content.Intent

/**
 * 通用详细配置界面
 *
 * 创建通用UI层，复用现有数据提供器，将卡片展开配置改为导航到新界面。
 * 支持任意类型的配置项列表，提供统一的卡片点击导航逻辑。
 *
 * @param T 配置项操作类型的泛型参数
 * @param title 界面标题
 * @param dataProviderGetter 数据提供器获取函数，返回配置项列表
 * @param initialExpandedItemId 初始展开的配置项ID（编辑模式使用）
 * @param initialConfigObject 初始配置对象（编辑模式使用）
 * @param onConfigurationComplete 配置完成回调，传递配置结果
 * @param onDismiss 取消回调
 * @param navController 导航控制器（可选）
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun <T> UniversalDetailConfigurationScreen(
    title: String,
    dataProviderGetter: (android.content.Context) -> List<ConfigurationCardItem<T>>,
    initialExpandedItemId: String? = null,
    initialConfigObject: Any? = null,
    onConfigurationComplete: (Any) -> Unit,
    onDismiss: () -> Unit,
    navController: NavController? = null
) {
    val context = LocalContext.current
    val configurationItems = remember(context) { dataProviderGetter(context) }

    // 创建ActivityResultLauncher来处理ItemConfigurationActivity的返回结果
    val itemConfigLauncher = rememberLauncherForActivityResult(
        contract = androidx.activity.result.contract.ActivityResultContracts.StartActivityForResult()
    ) { result ->
        android.util.Log.d("UniversalDetailConfigurationScreen", "ItemConfigurationActivity result received: resultCode=${result.resultCode}")
        if (result.resultCode == android.app.Activity.RESULT_OK) {
            val navigationKey = result.data?.getStringExtra("navigation_key")
            val itemId = result.data?.getStringExtra("item_id")
            val editIndex = result.data?.getIntExtra("edit_index", -1)?.takeIf { it != -1 }

            android.util.Log.d("UniversalDetailConfigurationScreen", "ItemConfigurationActivity result data: navigationKey=$navigationKey, itemId=$itemId, editIndex=$editIndex")

            if (navigationKey != null) {
                // 从NavigationDataStorageManager加载配置数据
                val navigationDataManager = com.weinuo.quickcommands.storage.NavigationDataStorageManager(context)
                val configData = navigationDataManager.loadSimpleNavigationData(navigationKey)

                android.util.Log.d("UniversalDetailConfigurationScreen", "Loaded config data: $configData")

                // 触发配置完成回调
                onConfigurationComplete(configData)

                // 清理导航数据
                navigationDataManager.clearNavigationData(navigationKey)
            }
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(title) },
                navigationIcon = {
                    IconButton(onClick = onDismiss) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = stringResource(R.string.back)
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        // 如果提供了 navController，则通过 CompositionLocalProvider 提供给子组件
        val content = @Composable {
            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues),
                verticalArrangement = Arrangement.spacedBy(8.dp),
                contentPadding = PaddingValues(vertical = 8.dp)
            ) {
                items(configurationItems) { item ->
                    NonExpandableConfigurationCard<Any>(
                        title = item.title,
                        description = item.description,
                        operationType = item.operationType as Any,
                        permissionRequired = item.permissionRequired,
                        onClick = {
                            // 导航到具体配置界面
                            navController?.navigate(
                                com.weinuo.quickcommands.navigation.Screen.ItemConfiguration.createRoute(item.id)
                            )
                        }
                    )
                }

                // 底部间距
                item {
                    Spacer(modifier = Modifier.height(16.dp))
                }
            }
        }

        if (navController != null) {
            CompositionLocalProvider(LocalNavController provides navController) {
                content()
            }
        } else {
            content()
        }
    }
}

/**
 * 通用详细配置内容组件（不带标题栏）
 *
 * 纯内容组件，不包含Scaffold和TopAppBar，适用于已有标题栏的Activity中使用。
 * 支持任意类型的配置项列表，提供统一的卡片点击导航逻辑。
 *
 * @param T 配置项操作类型的泛型参数
 * @param dataProviderGetter 数据提供器获取函数，返回配置项列表
 * @param initialExpandedItemId 初始展开的配置项ID（编辑模式使用）
 * @param initialConfigObject 初始配置对象（编辑模式使用）
 * @param onConfigurationComplete 配置完成回调，传递配置结果
 * @param navController 导航控制器（可选）
 */
@Composable
fun <T> UniversalDetailConfigurationContent(
    dataProviderGetter: (android.content.Context) -> List<ConfigurationCardItem<T>>,
    initialExpandedItemId: String? = null,
    initialConfigObject: Any? = null,
    onConfigurationComplete: (Any) -> Unit,
    navController: NavController? = null
) {
    val context = LocalContext.current
    val configurationItems = remember(context) { dataProviderGetter(context) }

    // 展开状态管理 - 同时只能展开一个卡片
    var expandedCardId by remember { mutableStateOf(initialExpandedItemId) }

    // 如果提供了 navController，则通过 CompositionLocalProvider 提供给子组件
    val content = @Composable {
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            verticalArrangement = Arrangement.spacedBy(8.dp),
            contentPadding = PaddingValues(vertical = 8.dp)
        ) {
            items(configurationItems) { item ->
                if (navController != null) {
                    // 有NavController：使用NonExpandableConfigurationCard，点击导航到新界面
                    NonExpandableConfigurationCard<Any>(
                        title = item.title,
                        description = item.description,
                        operationType = item.operationType as Any,
                        permissionRequired = item.permissionRequired,
                        onClick = {
                            // 导航到具体配置界面
                            navController.navigate(
                                com.weinuo.quickcommands.navigation.Screen.ItemConfiguration.createRoute(item.id)
                            )
                        }
                    )
                } else {
                    // 无NavController：使用NonExpandableConfigurationCard，点击启动新Activity
                    NonExpandableConfigurationCard<Any>(
                        title = item.title,
                        description = item.description,
                        operationType = item.operationType as Any,
                        permissionRequired = item.permissionRequired,
                        onClick = {
                            // 启动ItemConfigurationActivity来显示具体配置内容
                            com.weinuo.quickcommands.ui.activities.ItemConfigurationActivity.startForConfiguration(
                                context = context,
                                itemId = item.id,
                                itemTitle = item.title,
                                initialConfig = initialConfigObject
                            )
                        }
                    )
                }
            }

            // 底部间距
            item {
                Spacer(modifier = Modifier.height(16.dp))
            }
        }
    }

    if (navController != null) {
        CompositionLocalProvider(LocalNavController provides navController) {
            content()
        }
    } else {
        content()
    }
}
