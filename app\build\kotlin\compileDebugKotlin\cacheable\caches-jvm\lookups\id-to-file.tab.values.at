onfigurationActivity.kt.ktapp/src/main/java/com/weinuo/quickcommands/ui/components/NonExpandableConfigurationCard.kt/src/main/java/com/weinuo/quickcommands/data/AppRepository.ktJ Iapp/src/main/java/com/weinuo/quickcommands/data/PhoneCheckupRepository.ktJ Iapp/src/main/java/com/weinuo/quickcommands/data/QuickCommandRepository.ktF Eapp/src/main/java/com/weinuo/quickcommands/data/SettingsRepository.ktQ Papp/src/main/java/com/weinuo/quickcommands/execution/SharedConditionEvaluator.ktO Napp/src/main/java/com/weinuo/quickcommands/execution/SharedExecutionHandler.ktK Japp/src/main/java/com/weinuo/quickcommands/floating/AcceleratorBallView.ktW Vapp/src/main/java/com/weinuo/quickcommands/floating/AdvancedFloatingRecordingButton.ktX Wapp/src/main/java/com/weinuo/quickcommands/floating/AdvancedFloatingRecordingManager.ktX Wapp/src/main/java/com/weinuo/quickcommands/floating/AdvancedFloatingRecordingService.ktO Napp/src/main/java/com/weinuo/quickcommands/floating/FloatingAcceleratorBall.ktR Qapp/src/main/java/com/weinuo/quickcommands/floating/FloatingAcceleratorManager.ktR Qapp/src/main/java/com/weinuo/quickcommands/floating/FloatingAcceleratorService.ktM Lapp/src/main/java/com/weinuo/quickcommands/floating/FloatingButtonManager.ktO Napp/src/main/java/com/weinuo/quickcommands/floating/FloatingRecordingButton.ktP Oapp/src/main/java/com/weinuo/quickcommands/floating/FloatingRecordingManager.ktV Uapp/src/main/java/com/weinuo/quickcommands/floating/FloatingRecordingResultManager.ktP Oapp/src/main/java/com/weinuo/quickcommands/floating/FloatingRecordingService.ktH Gapp/src/main/java/com/weinuo/quickcommands/floating/GestureForwarder.ktH Gapp/src/main/java/com/weinuo/quickcommands/floating/NumberMarkerView.ktH Gapp/src/main/java/com/weinuo/quickcommands/floating/RecordingOverlay.ktJ Iapp/src/main/java/com/weinuo/quickcommands/floating/SwipeEndMarkerView.ktP Oapp/src/main/java/com/weinuo/quickcommands/gesture/FingerprintGestureManager.ktH Gapp/src/main/java/com/weinuo/quickcommands/model/AdvancedGestureType.ktI Happ/src/main/java/com/weinuo/quickcommands/model/AdvancedMemoryConfig.kt= <app/src/main/java/com/weinuo/quickcommands/model/AppModel.ktD Capp/src/main/java/com/weinuo/quickcommands/model/CommandTemplate.ktC Bapp/src/main/java/com/weinuo/quickcommands/model/PixelColorInfo.ktL Kapp/src/main/java/com/weinuo/quickcommands/model/PresetCleanupStrategies.ktF Eapp/src/main/java/com/weinuo/quickcommands/model/QuickCommandModel.ktB Aapp/src/main/java/com/weinuo/quickcommands/model/SettingsModel.kt@ ?app/src/main/java/com/weinuo/quickcommands/model/ShareTarget.ktH Gapp/src/main/java/com/weinuo/quickcommands/model/SmartReminderConfig.ktF Eapp/src/main/java/com/weinuo/quickcommands/model/SmartReminderType.kt? >app/src/main/java/com/weinuo/quickcommands/model/TouchEvent.ktE Dapp/src/main/java/com/weinuo/quickcommands/model/shared_task_list.ktR Qapp/src/main/java/com/weinuo/quickcommands/model/shared_trigger_condition_list.ktR Qapp/src/main/java/com/weinuo/quickcommands/monitoring/AppStateConditionMonitor.ktI Happ/src/main/java/com/weinuo/quickcommands/monitoring/AppStateMonitor.ktM Lapp/src/main/java/com/weinuo/quickcommands/monitoring/BatteryStateMonitor.ktS Rapp/src/main/java/com/weinuo/quickcommands/monitoring/CommunicationStateMonitor.ktP Oapp/src/main/java/com/weinuo/quickcommands/monitoring/ConnectionStateMonitor.ktL Kapp/src/main/java/com/weinuo/quickcommands/monitoring/DeviceEventMonitor.ktS Rapp/src/main/java/com/weinuo/quickcommands/monitoring/IntelligentMemoryDetector.ktL Kapp/src/main/java/com/weinuo/quickcommands/monitoring/MemoryStateMonitor.ktL Kapp/src/main/java/com/weinuo/quickcommands/monitoring/SensorStateMonitor.ktN Mapp/src/main/java/com/weinuo/quickcommands/monitoring/TimeConditionMonitor.ktD Capp/src/main/java/com/weinuo/quickcommands/navigation/Navigation.ktL Kapp/src/main/java/com/weinuo/quickcommands/navigation/ThemedBottomNavBar.ktM Lapp/src/main/java/com/weinuo/quickcommands/permission/DeviceAdminReceiver.ktQ Papp/src/main/java/com/weinuo/quickcommands/permission/GlobalPermissionManager.ktQ Papp/src/main/java/com/weinuo/quickcommands/permission/GlobalSettingsOperation.ktZ Yapp/src/main/java/com/weinuo/quickcommands/permission/PermissionAwareOperationSelector.ktL Kapp/src/main/java/com/weinuo/quickcommands/permission/PermissionRegistry.ktU Tapp/src/main/java/com/weinuo/quickcommands/permission/ShellScriptShizukuOperation.ktP Oapp/src/main/java/com/weinuo/quickcommands/permission/SmartReminderOperation.ktG Fapp/src/main/java/com/weinuo/quickcommands/receiver/PackageReceiver.ktM Lapp/src/main/java/com/weinuo/quickcommands/repository/ConditionRepository.ktH Gapp/src/main/java/com/weinuo/quickcommands/repository/TaskRepository.ktJ Iapp/src/main/java/com/weinuo/quickcommands/service/AlarmOverlayService.ktV Uapp/src/main/java/com/weinuo/quickcommands/service/AutoClickerAccessibilityService.ktU Tapp/src/main/java/com/weinuo/quickcommands/service/ClipboardRefreshOverlayService.ktL Kapp/src/main/java/com/weinuo/quickcommands/service/FloatingButtonService.kt] \app/src/main/java/com/weinuo/quickcommands/service/GestureRecognitionAccessibilityService.ktR Qapp/src/main/java/com/weinuo/quickcommands/service/HomeButtonLongPressDetector.kt_ ^app/src/main/java/com/weinuo/quickcommands/service/InterfaceInteractionAccessibilityService.ktK Japp/src/main/java/com/weinuo/quickcommands/service/ManualTriggerManager.ktL Kapp/src/main/java/com/weinuo/quickcommands/service/MediaKeyPressDetector.ktW Vapp/src/main/java/com/weinuo/quickcommands/service/QuickCommandsNotificationService.ktK Japp/src/main/java/com/weinuo/quickcommands/service/QuickCommandsService.ktR Qapp/src/main/java/com/weinuo/quickcommands/service/SmartReminderOverlayService.ktZ Yapp/src/main/java/com/weinuo/quickcommands/service/SystemOperationAccessibilityService.ktW Vapp/src/main/java/com/weinuo/quickcommands/service/SystemPriorityEnhancementService.ktO Napp/src/main/java/com/weinuo/quickcommands/service/TouchBlockOverlayService.ktI Happ/src/main/java/com/weinuo/quickcommands/service/TouchEventRecorder.ktM Lapp/src/main/java/com/weinuo/quickcommands/service/VolumeKeyPressDetector.ktE Dapp/src/main/java/com/weinuo/quickcommands/shizuku/ShizukuManager.ktT Sapp/src/main/java/com/weinuo/quickcommands/shortcut/QuickCommandExecutorActivity.ktG Fapp/src/main/java/com/weinuo/quickcommands/shortcut/ShortcutManager.ktU Tapp/src/main/java/com/weinuo/quickcommands/shortcut/StaticShortcutHandlerActivity.ktL Kapp/src/main/java/com/weinuo/quickcommands/smartreminder/AddressDetector.ktS Rapp/src/main/java/com/weinuo/quickcommands/smartreminder/AddressReminderHandler.ktL Kapp/src/main/java/com/weinuo/quickcommands/smartreminder/AppLinkDetector.ktS Rapp/src/main/java/com/weinuo/quickcommands/smartreminder/AppLinkReminderHandler.ktV Uapp/src/main/java/com/weinuo/quickcommands/smartreminder/FlashlightReminderHandler.ktT Sapp/src/main/java/com/weinuo/quickcommands/smartreminder/MusicAppReminderHandler.ktR Qapp/src/main/java/com/weinuo/quickcommands/smartreminder/NewAppReminderHandler.ktZ Yapp/src/main/java/com/weinuo/quickcommands/smartreminder/ScreenRotationReminderHandler.ktM Lapp/src/main/java/com/weinuo/quickcommands/smartreminder/ShareUrlDetector.ktT Sapp/src/main/java/com/weinuo/quickcommands/smartreminder/ShareUrlReminderHandler.ktW Vapp/src/main/java/com/weinuo/quickcommands/smartreminder/ShoppingAppReminderHandler.ktQ Papp/src/main/java/com/weinuo/quickcommands/smartreminder/ShoppingLinkDetector.ktQ Papp/src/main/java/com/weinuo/quickcommands/smartreminder/SmartReminderManager.ktR Qapp/src/main/java/com/weinuo/quickcommands/storage/AdvancedMemoryConfigStorage.ktK Japp/src/main/java/com/weinuo/quickcommands/storage/AppListStorageEngine.ktT Sapp/src/main/java/com/weinuo/quickcommands/storage/GestureRecordingNativeManager.ktO Napp/src/main/java/com/weinuo/quickcommands/storage/NativeTypeStorageManager.ktS Rapp/src/main/java/com/weinuo/quickcommands/storage/NavigationDataStorageManager.ktU Tapp/src/main/java/com/weinuo/quickcommands/storage/QuickCommandStorageCoordinator.ktQ Papp/src/main/java/com/weinuo/quickcommands/storage/SmartReminderConfigAdapter.ktC Bapp/src/main/java/com/weinuo/quickcommands/storage/StorageTypes.ktL Kapp/src/main/java/com/weinuo/quickcommands/storage/UIStateStorageManager.ktX Wapp/src/main/java/com/weinuo/quickcommands/storage/adapters/AppStateConditionAdapter.ktV Uapp/src/main/java/com/weinuo/quickcommands/storage/adapters/ApplicationTaskAdapter.ktT Sapp/src/main/java/com/weinuo/quickcommands/storage/adapters/BaseConditionAdapter.ktO Napp/src/main/java/com/weinuo/quickcommands/storage/adapters/BaseTaskAdapter.ktQ Papp/src/main/java/com/weinuo/quickcommands/storage/adapters/CameraTaskAdapter.ktb aapp/src/main/java/com/weinuo/quickcommands/storage/adapters/CommunicationStateConditionAdapter.ktW Vapp/src/main/java/com/weinuo/quickcommands/storage/adapters/ConditionAdapterManager.kt_ ^app/src/main/java/com/weinuo/quickcommands/storage/adapters/ConnectionStateConditionAdapter.ktW Vapp/src/main/java/com/weinuo/quickcommands/storage/adapters/ConnectivityTaskAdapter.ktS Rapp/src/main/java/com/weinuo/quickcommands/storage/adapters/DateTimeTaskAdapter.ktW Vapp/src/main/java/com/weinuo/quickcommands/storage/adapters/DeviceActionTaskAdapter.kt[ Zapp/src/main/java/com/weinuo/quickcommands/storage/adapters/DeviceEventConditionAdapter.ktY Xapp/src/main/java/com/weinuo/quickcommands/storage/adapters/DeviceSettingsTaskAdapter.ktX Wapp/src/main/java/com/weinuo/quickcommands/storage/adapters/FileOperationTaskAdapter.kt^ ]app/src/main/java/com/weinuo/quickcommands/storage/adapters/GestureRecordingStorageAdapter.ktV Uapp/src/main/java/com/weinuo/quickcommands/storage/adapters/InformationTaskAdapter.ktS Rapp/src/main/java/com/weinuo/quickcommands/storage/adapters/LocationTaskAdapter.kt] \app/src/main/java/com/weinuo/quickcommands/storage/adapters/ManualTriggerConditionAdapter.ktP Oapp/src/main/java/com/weinuo/quickcommands/storage/adapters/MediaTaskAdapter.ktW Vapp/src/main/java/com/weinuo/quickcommands/storage/adapters/NotificationTaskAdapter.ktP Oapp/src/main/java/com/weinuo/quickcommands/storage/adapters/PhoneTaskAdapter.ktX Wapp/src/main/java/com/weinuo/quickcommands/storage/adapters/ScreenControlTaskAdapter.kt[ Zapp/src/main/java/com/weinuo/quickcommands/storage/adapters/SensorStateConditionAdapter.ktR Qapp/src/main/java/com/weinuo/quickcommands/storage/adapters/TaskAdapterManager.ktY Xapp/src/main/java/com/weinuo/quickcommands/storage/adapters/TimeBasedConditionAdapter.ktQ Papp/src/main/java/com/weinuo/quickcommands/storage/adapters/VolumeTaskAdapter.kt@ ?app/src/main/java/com/weinuo/quickcommands/ui/BubbleActivity.ktS Rapp/src/main/java/com/weinuo/quickcommands/ui/activities/AddCleanupRuleActivity.kt\ [app/src/main/java/com/weinuo/quickcommands/ui/activities/AdvancedCleanupStrategyActivity.ktY Xapp/src/main/java/com/weinuo/quickcommands/ui/activities/AdvancedMemoryConfigActivity.ktQ Papp/src/main/java/com/weinuo/quickcommands/ui/activities/AppSelectionActivity.ktU Tapp/src/main/java/com/weinuo/quickcommands/ui/activities/ContactSelectionActivity.ktZ Yapp/src/main/java/com/weinuo/quickcommands/ui/activities/DetailedConfigurationActivity.ktR Qapp/src/main/java/com/weinuo/quickcommands/ui/activities/IconSelectionActivity.ktW Vapp/src/main/java/com/weinuo/quickcommands/ui/activities/MemoryLearningDataActivity.ktU Tapp/src/main/java/com/weinuo/quickcommands/ui/activities/QuickCommandFormActivity.ktV Uapp/src/main/java/com/weinuo/quickcommands/ui/activities/RingtoneSelectionActivity.ktY Xapp/src/main/java/com/weinuo/quickcommands/ui/activities/ShareTargetSelectionActivity.kt^ ]app/src/main/java/com/weinuo/quickcommands/ui/activities/SmartReminderDetailConfigActivity.ktY Xapp/src/main/java/com/weinuo/quickcommands/ui/activities/UnifiedConfigurationActivity.ktH Gapp/src/main/java/com/weinuo/quickcommands/ui/components/AppListItem.ktP Oapp/src/main/java/com/weinuo/quickcommands/ui/components/CategoryDisplayGrid.ktP Oapp/src/main/java/com/weinuo/quickcommands/ui/components/CircleCropImageView.ktM Lapp/src/main/java/com/weinuo/quickcommands/ui/components/ColorSettingItem.ktN Mapp/src/main/java/com/weinuo/quickcommands/ui/components/CommandComponents.ktX Wapp/src/main/java/com/weinuo/quickcommands/ui/components/ExpandableConfigurationCard.ktQ Papp/src/main/java/com/weinuo/quickcommands/ui/components/MotionSensingManager.kt[ Zapp/src/main/java/com/weinuo/quickcommands/ui/components/NonExpandableConfigurationCard.ktQ Papp/src/main/java/com/weinuo/quickcommands/ui/components/PreferenceComponents.kt^ ]app/src/main/java/com/weinuo/quickcommands/ui/components/QuickCommandSelectionBottomAppBar.ktY Xapp/src/main/java/com/weinuo/quickcommands/ui/components/RingtoneConfigurationContent.ktR Qapp/src/main/java/com/weinuo/quickcommands/ui/components/ScrollableAlertDialog.ktK Japp/src/main/java/com/weinuo/quickcommands/ui/components/SelectionState.ktK Japp/src/main/java/com/weinuo/quickcommands/ui/components/ShizukuTipCard.ktR Qapp/src/main/java/com/weinuo/quickcommands/ui/components/SkipOptionsComponents.ktZ Yapp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallAdvancedColorSchemes.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/WaterBallComponent.ktb aapp/src/main/java/com/weinuo/quickcommands/ui/components/integrated/IntegratedBottomNavigation.ktX Wapp/src/main/java/com/weinuo/quickcommands/ui/components/integrated/IntegratedButton.ktV Uapp/src/main/java/com/weinuo/quickcommands/ui/components/integrated/IntegratedCard.kt\ [app/src/main/java/com/weinuo/quickcommands/ui/components/integrated/IntegratedMainLayout.kt` _app/src/main/java/com/weinuo/quickcommands/ui/components/integrated/IntegratedScrollBehavior.kta `app/src/main/java/com/weinuo/quickcommands/ui/components/integrated/IntegratedSearchTextField.kt[ Zapp/src/main/java/com/weinuo/quickcommands/ui/components/integrated/IntegratedTextField.kt[ Zapp/src/main/java/com/weinuo/quickcommands/ui/components/integrated/IntegratedTopAppBar.ktc bapp/src/main/java/com/weinuo/quickcommands/ui/components/integrated/IntegratedTopAppBarProvider.kt\ [app/src/main/java/com/weinuo/quickcommands/ui/components/layered/LayeredBottomNavigation.ktR Qapp/src/main/java/com/weinuo/quickcommands/ui/components/layered/LayeredButton.ktP Oapp/src/main/java/com/weinuo/quickcommands/ui/components/layered/LayeredCard.ktV Uapp/src/main/java/com/weinuo/quickcommands/ui/components/layered/LayeredMainLayout.ktW Vapp/src/main/java/com/weinuo/quickcommands/ui/components/layered/LayeredRadioButton.kt[ Zapp/src/main/java/com/weinuo/quickcommands/ui/components/layered/LayeredSearchTextField.ktU Tapp/src/main/java/com/weinuo/quickcommands/ui/components/layered/LayeredTextField.ktU Tapp/src/main/java/com/weinuo/quickcommands/ui/components/layered/LayeredTopAppBar.ktm lapp/src/main/java/com/weinuo/quickcommands/ui/components/oceanblue/OceanBlueAddressReminderConfigProvider.ktm lapp/src/main/java/com/weinuo/quickcommands/ui/components/oceanblue/OceanBlueAppLinkReminderConfigProvider.ktS Rapp/src/main/java/com/weinuo/quickcommands/ui/components/oceanblue/OceanBlueFAB.ktp oapp/src/main/java/com/weinuo/quickcommands/ui/components/oceanblue/OceanBlueFlashlightReminderConfigProvider.ktn mapp/src/main/java/com/weinuo/quickcommands/ui/components/oceanblue/OceanBlueMusicAppReminderConfigProvider.ktl kapp/src/main/java/com/weinuo/quickcommands/ui/components/oceanblue/OceanBlueNewAppReminderConfigProvider.ktt sapp/src/main/java/com/weinuo/quickcommands/ui/components/oceanblue/OceanBlueScreenRotationReminderConfigProvider.ktn mapp/src/main/java/com/weinuo/quickcommands/ui/components/oceanblue/OceanBlueShareUrlReminderConfigProvider.ktq papp/src/main/java/com/weinuo/quickcommands/ui/components/oceanblue/OceanBlueShoppingAppReminderConfigProvider.ktW Vapp/src/main/java/com/weinuo/quickcommands/ui/components/skyblue/IOSStyleTimePicker.kti happ/src/main/java/com/weinuo/quickcommands/ui/components/skyblue/SkyBlueAddressReminderConfigProvider.kti happ/src/main/java/com/weinuo/quickcommands/ui/components/skyblue/SkyBlueAppLinkReminderConfigProvider.ktV Uapp/src/main/java/com/weinuo/quickcommands/ui/components/skyblue/SkyBlueCardButton.ktR Qapp/src/main/java/com/weinuo/quickcommands/ui/components/skyblue/SkyBlueDialog.ktZ Yapp/src/main/java/com/weinuo/quickcommands/ui/components/skyblue/SkyBlueDialogExamples.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/components/skyblue/SkyBlueFAB.ktl kapp/src/main/java/com/weinuo/quickcommands/ui/components/skyblue/SkyBlueFlashlightReminderConfigProvider.ktj iapp/src/main/java/com/weinuo/quickcommands/ui/components/skyblue/SkyBlueMusicAppReminderConfigProvider.kth gapp/src/main/java/com/weinuo/quickcommands/ui/components/skyblue/SkyBlueNewAppReminderConfigProvider.ktW Vapp/src/main/java/com/weinuo/quickcommands/ui/components/skyblue/SkyBlueRadioButton.ktp oapp/src/main/java/com/weinuo/quickcommands/ui/components/skyblue/SkyBlueScreenRotationReminderConfigProvider.ktj iapp/src/main/java/com/weinuo/quickcommands/ui/components/skyblue/SkyBlueShareUrlReminderConfigProvider.ktm lapp/src/main/java/com/weinuo/quickcommands/ui/components/skyblue/SkyBlueShoppingAppReminderConfigProvider.ktR Qapp/src/main/java/com/weinuo/quickcommands/ui/components/skyblue/SkyBlueSwitch.kt[ Zapp/src/main/java/com/weinuo/quickcommands/ui/components/skyblue/SkyBlueTopAppBarButton.ktZ Yapp/src/main/java/com/weinuo/quickcommands/ui/components/themed/ThemedBottomNavigation.ktU Tapp/src/main/java/com/weinuo/quickcommands/ui/components/themed/ThemedBottomSheet.ktP Oapp/src/main/java/com/weinuo/quickcommands/ui/components/themed/ThemedButton.ktN Mapp/src/main/java/com/weinuo/quickcommands/ui/components/themed/ThemedCard.kt] \app/src/main/java/com/weinuo/quickcommands/ui/components/themed/ThemedCommandTemplateCard.ktf eapp/src/main/java/com/weinuo/quickcommands/ui/components/themed/ThemedCommandTemplateCardWithImage.ktP Oapp/src/main/java/com/weinuo/quickcommands/ui/components/themed/ThemedDialog.kt^ ]app/src/main/java/com/weinuo/quickcommands/ui/components/themed/ThemedFloatingActionButton.ktT Sapp/src/main/java/com/weinuo/quickcommands/ui/components/themed/ThemedMainLayout.ktZ Yapp/src/main/java/com/weinuo/quickcommands/ui/components/themed/ThemedQuickCommandCard.ktU Tapp/src/main/java/com/weinuo/quickcommands/ui/components/themed/ThemedRadioButton.ktR Qapp/src/main/java/com/weinuo/quickcommands/ui/components/themed/ThemedScaffold.ktY Xapp/src/main/java/com/weinuo/quickcommands/ui/components/themed/ThemedSearchTextField.kt[ Zapp/src/main/java/com/weinuo/quickcommands/ui/components/themed/ThemedSmartReminderCard.ktS Rapp/src/main/java/com/weinuo/quickcommands/ui/components/themed/ThemedTextField.ktV Uapp/src/main/java/com/weinuo/quickcommands/ui/configuration/AppStateConfigProvider.kt] \app/src/main/java/com/weinuo/quickcommands/ui/configuration/ApplicationTaskConfigProvider.kt[ Zapp/src/main/java/com/weinuo/quickcommands/ui/configuration/AutoClickerConfigComponents.ktZ Yapp/src/main/java/com/weinuo/quickcommands/ui/configuration/BatteryStateConfigProvider.ktX Wapp/src/main/java/com/weinuo/quickcommands/ui/configuration/CameraTaskConfigProvider.kt` _app/src/main/java/com/weinuo/quickcommands/ui/configuration/CommunicationStateConfigProvider.ktY Xapp/src/main/java/com/weinuo/quickcommands/ui/configuration/ConfigurationDataProvider.ktR Qapp/src/main/java/com/weinuo/quickcommands/ui/configuration/ConfigurationTypes.kt] \app/src/main/java/com/weinuo/quickcommands/ui/configuration/ConnectionStateConfigProvider.kt^ ]app/src/main/java/com/weinuo/quickcommands/ui/configuration/ConnectivityTaskConfigProvider.ktZ Yapp/src/main/java/com/weinuo/quickcommands/ui/configuration/DateTimeTaskConfigProvider.kt^ ]app/src/main/java/com/weinuo/quickcommands/ui/configuration/DeviceActionTaskConfigProvider.ktY Xapp/src/main/java/com/weinuo/quickcommands/ui/configuration/DeviceEventConfigProvider.kt` _app/src/main/java/com/weinuo/quickcommands/ui/configuration/DeviceSettingsTaskConfigProvider.kt_ ^app/src/main/java/com/weinuo/quickcommands/ui/configuration/FileOperationTaskConfigProvider.kt] \app/src/main/java/com/weinuo/quickcommands/ui/configuration/InformationTaskConfigProvider.ktZ Yapp/src/main/java/com/weinuo/quickcommands/ui/configuration/LocationTaskConfigProvider.kt[ Zapp/src/main/java/com/weinuo/quickcommands/ui/configuration/ManualTriggerConfigProvider.ktW Vapp/src/main/java/com/weinuo/quickcommands/ui/configuration/MediaTaskConfigProvider.kt^ ]app/src/main/java/com/weinuo/quickcommands/ui/configuration/NotificationTaskConfigProvider.ktW Vapp/src/main/java/com/weinuo/quickcommands/ui/configuration/PhoneTaskConfigProvider.kt_ ^app/src/main/java/com/weinuo/quickcommands/ui/configuration/ScreenControlTaskConfigProvider.ktY Xapp/src/main/java/com/weinuo/quickcommands/ui/configuration/SensorStateConfigProvider.ktY Xapp/src/main/java/com/weinuo/quickcommands/ui/configuration/SmartReminderDataProvider.ktR Qapp/src/main/java/com/weinuo/quickcommands/ui/configuration/TaskConfigProvider.kt[ Zapp/src/main/java/com/weinuo/quickcommands/ui/configuration/TimeConditionConfigProvider.ktX Wapp/src/main/java/com/weinuo/quickcommands/ui/configuration/VolumeTaskConfigProvider.ktP Oapp/src/main/java/com/weinuo/quickcommands/ui/effects/BackgroundBlurModifier.ktE Dapp/src/main/java/com/weinuo/quickcommands/ui/effects/HazeManager.ktL Kapp/src/main/java/com/weinuo/quickcommands/ui/recording/ActionEditScreen.ktT Sapp/src/main/java/com/weinuo/quickcommands/ui/recording/GestureRecordingActivity.ktP Oapp/src/main/java/com/weinuo/quickcommands/ui/recording/GestureRecordingArea.ktX Wapp/src/main/java/com/weinuo/quickcommands/ui/recording/GestureRecordingEditActivity.ktV Uapp/src/main/java/com/weinuo/quickcommands/ui/recording/GestureRecordingEditScreen.ktY Xapp/src/main/java/com/weinuo/quickcommands/ui/recording/GestureRecordingEditViewModel.ktU Tapp/src/main/java/com/weinuo/quickcommands/ui/recording/GestureRecordingViewModel.ktP Oapp/src/main/java/com/weinuo/quickcommands/ui/recording/PositionPickerScreen.ktQ Papp/src/main/java/com/weinuo/quickcommands/ui/recording/RecordingModeSelector.ktP Oapp/src/main/java/com/weinuo/quickcommands/ui/screens/AccountSelectionScreen.ktN Mapp/src/main/java/com/weinuo/quickcommands/ui/screens/AddCleanupRuleScreen.ktW Vapp/src/main/java/com/weinuo/quickcommands/ui/screens/AdvancedCleanupStrategyScreen.ktT Sapp/src/main/java/com/weinuo/quickcommands/ui/screens/AdvancedMemoryConfigScreen.ktW Vapp/src/main/java/com/weinuo/quickcommands/ui/screens/AppImportanceManagementScreen.ktL Kapp/src/main/java/com/weinuo/quickcommands/ui/screens/AppSelectionScreen.ktP Oapp/src/main/java/com/weinuo/quickcommands/ui/screens/ContactSelectionScreen.ktW Vapp/src/main/java/com/weinuo/quickcommands/ui/screens/CustomAppPlatformConfigScreen.kt\ [app/src/main/java/com/weinuo/quickcommands/ui/screens/CustomShoppingPlatformConfigScreen.ktS Rapp/src/main/java/com/weinuo/quickcommands/ui/screens/DetailConfigurationScreen.ktU Tapp/src/main/java/com/weinuo/quickcommands/ui/screens/DetailedConfigurationScreen.ktQ Papp/src/main/java/com/weinuo/quickcommands/ui/screens/ItemConfigurationScreen.ktR Qapp/src/main/java/com/weinuo/quickcommands/ui/screens/MemoryLearningDataScreen.ktP Oapp/src/main/java/com/weinuo/quickcommands/ui/screens/QuickCommandFormScreen.ktQ Papp/src/main/java/com/weinuo/quickcommands/ui/screens/RingtoneSelectionScreen.ktG Fapp/src/main/java/com/weinuo/quickcommands/ui/screens/ScreenFactory.ktT Sapp/src/main/java/com/weinuo/quickcommands/ui/screens/ShareTargetSelectionScreen.ktY Xapp/src/main/java/com/weinuo/quickcommands/ui/screens/SmartReminderDetailConfigScreen.ktR Qapp/src/main/java/com/weinuo/quickcommands/ui/screens/StopwatchSelectionScreen.ktT Sapp/src/main/java/com/weinuo/quickcommands/ui/screens/UnifiedConfigurationScreen.kt\ [app/src/main/java/com/weinuo/quickcommands/ui/screens/UniversalDetailConfigurationScreen.ktc bapp/src/main/java/com/weinuo/quickcommands/ui/screens/oceanblue/OceanBlueCommandTemplatesScreen.kta `app/src/main/java/com/weinuo/quickcommands/ui/screens/oceanblue/OceanBlueGlobalSettingsScreen.kt_ ^app/src/main/java/com/weinuo/quickcommands/ui/screens/oceanblue/OceanBluePhoneCheckupScreen.kt` _app/src/main/java/com/weinuo/quickcommands/ui/screens/oceanblue/OceanBlueQuickCommandsScreen.ktZ Yapp/src/main/java/com/weinuo/quickcommands/ui/screens/oceanblue/OceanBlueScreenFactory.kta `app/src/main/java/com/weinuo/quickcommands/ui/screens/oceanblue/OceanBlueSmartRemindersScreen.kt_ ^app/src/main/java/com/weinuo/quickcommands/ui/screens/skyblue/SkyBlueCommandTemplatesScreen.kt] \app/src/main/java/com/weinuo/quickcommands/ui/screens/skyblue/SkyBlueGlobalSettingsScreen.kt[ Zapp/src/main/java/com/weinuo/quickcommands/ui/screens/skyblue/SkyBluePhoneCheckupScreen.kt\ [app/src/main/java/com/weinuo/quickcommands/ui/screens/skyblue/SkyBlueQuickCommandsScreen.ktV Uapp/src/main/java/com/weinuo/quickcommands/ui/screens/skyblue/SkyBlueScreenFactory.kt] \app/src/main/java/com/weinuo/quickcommands/ui/screens/skyblue/SkyBlueSmartRemindersScreen.kt] \app/src/main/java/com/weinuo/quickcommands/ui/screens/themed/ThemedCommandTemplatesScreen.kt[ Zapp/src/main/java/com/weinuo/quickcommands/ui/screens/themed/ThemedGlobalSettingsScreen.ktY Xapp/src/main/java/com/weinuo/quickcommands/ui/screens/themed/ThemedPhoneCheckupScreen.ktZ Yapp/src/main/java/com/weinuo/quickcommands/ui/screens/themed/ThemedQuickCommandsScreen.ktN Mapp/src/main/java/com/weinuo/quickcommands/ui/screens/themed/ThemedScreens.kt[ Zapp/src/main/java/com/weinuo/quickcommands/ui/screens/themed/ThemedSmartRemindersScreen.ktM Lapp/src/main/java/com/weinuo/quickcommands/ui/settings/SettingsComponents.ktJ Iapp/src/main/java/com/weinuo/quickcommands/ui/settings/ThemeOptionCard.kt= <app/src/main/java/com/weinuo/quickcommands/ui/theme/Color.kt= <app/src/main/java/com/weinuo/quickcommands/ui/theme/Theme.kt< ;app/src/main/java/com/weinuo/quickcommands/ui/theme/Type.ktP Oapp/src/main/java/com/weinuo/quickcommands/ui/theme/config/BlurConfiguration.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/theme/config/ComponentConfigs.ktK Japp/src/main/java/com/weinuo/quickcommands/ui/theme/config/StyleConfigs.ktS Rapp/src/main/java/com/weinuo/quickcommands/ui/theme/examples/ThemeSystemExample.ktX Wapp/src/main/java/com/weinuo/quickcommands/ui/theme/manager/BlurConfigurationManager.kti happ/src/main/java/com/weinuo/quickcommands/ui/theme/manager/BottomNavigationStyleConfigurationManager.kt] \app/src/main/java/com/weinuo/quickcommands/ui/theme/manager/CardStyleConfigurationManager.kta `app/src/main/java/com/weinuo/quickcommands/ui/theme/manager/DialogSpacingConfigurationManager.kt^ ]app/src/main/java/com/weinuo/quickcommands/ui/theme/manager/PageLayoutConfigurationManager.kt` _app/src/main/java/com/weinuo/quickcommands/ui/theme/manager/SkyBlueColorConfigurationManager.ktJ Iapp/src/main/java/com/weinuo/quickcommands/ui/theme/manager/ThemeCache.ktL Kapp/src/main/java/com/weinuo/quickcommands/ui/theme/manager/ThemeContext.ktL Kapp/src/main/java/com/weinuo/quickcommands/ui/theme/manager/ThemeManager.ktW Vapp/src/main/java/com/weinuo/quickcommands/ui/theme/manager/ThemePerformanceManager.ktW Vapp/src/main/java/com/weinuo/quickcommands/ui/theme/manager/ThemePerformanceMonitor.kt] \app/src/main/java/com/weinuo/quickcommands/ui/theme/manager/UISpacingConfigurationManager.kta `app/src/main/java/com/weinuo/quickcommands/ui/theme/oceanblue/OceanBlueAnimationConfiguration.ktV Uapp/src/main/java/com/weinuo/quickcommands/ui/theme/oceanblue/OceanBlueColorScheme.kt[ Zapp/src/main/java/com/weinuo/quickcommands/ui/theme/oceanblue/OceanBlueComponentFactory.ktc bapp/src/main/java/com/weinuo/quickcommands/ui/theme/oceanblue/OceanBlueInteractionConfiguration.kt] \app/src/main/java/com/weinuo/quickcommands/ui/theme/oceanblue/OceanBlueStyleConfiguration.ktX Wapp/src/main/java/com/weinuo/quickcommands/ui/theme/oceanblue/OceanBlueThemeProvider.ktQ Papp/src/main/java/com/weinuo/quickcommands/ui/theme/provider/AppThemeProvider.kt] \app/src/main/java/com/weinuo/quickcommands/ui/theme/skyblue/SkyBlueAnimationConfiguration.ktR Qapp/src/main/java/com/weinuo/quickcommands/ui/theme/skyblue/SkyBlueColorScheme.ktW Vapp/src/main/java/com/weinuo/quickcommands/ui/theme/skyblue/SkyBlueComponentFactory.ktZ Yapp/src/main/java/com/weinuo/quickcommands/ui/theme/skyblue/SkyBlueDialogConfigManager.kt_ ^app/src/main/java/com/weinuo/quickcommands/ui/theme/skyblue/SkyBlueInteractionConfiguration.ktY Xapp/src/main/java/com/weinuo/quickcommands/ui/theme/skyblue/SkyBlueStyleConfiguration.ktT Sapp/src/main/java/com/weinuo/quickcommands/ui/theme/skyblue/SkyBlueThemeProvider.ktG Fapp/src/main/java/com/weinuo/quickcommands/ui/theme/system/AppTheme.ktO Napp/src/main/java/com/weinuo/quickcommands/ui/theme/system/ComponentFactory.ktM Lapp/src/main/java/com/weinuo/quickcommands/ui/theme/system/DesignApproach.ktQ Papp/src/main/java/com/weinuo/quickcommands/ui/theme/system/StyleConfiguration.ktL Kapp/src/main/java/com/weinuo/quickcommands/ui/theme/system/ThemeProvider.ktH Gapp/src/main/java/com/weinuo/quickcommands/ui/utils/NativeStateSaver.ktM Lapp/src/main/java/com/weinuo/quickcommands/utils/AccessibilityServiceUtil.ktG Fapp/src/main/java/com/weinuo/quickcommands/utils/AppLanguageManager.ktD Capp/src/main/java/com/weinuo/quickcommands/utils/AppStateMonitor.ktL Kapp/src/main/java/com/weinuo/quickcommands/utils/BluetoothPermissionUtil.ktA @app/src/main/java/com/weinuo/quickcommands/utils/CacheManager.ktI Happ/src/main/java/com/weinuo/quickcommands/utils/CameraPermissionUtil.ktP Oapp/src/main/java/com/weinuo/quickcommands/utils/CommunicationPermissionUtil.ktC Bapp/src/main/java/com/weinuo/quickcommands/utils/ContactsHelper.ktN Mapp/src/main/java/com/weinuo/quickcommands/utils/DeviceEventPermissionUtil.ktP Oapp/src/main/java/com/weinuo/quickcommands/utils/ExperimentalFeatureDetector.ktC Bapp/src/main/java/com/weinuo/quickcommands/utils/FilePickerUtil.ktD Capp/src/main/java/com/weinuo/quickcommands/utils/IconFileManager.kt? >app/src/main/java/com/weinuo/quickcommands/utils/ImageUtils.ktZ Yapp/src/main/java/com/weinuo/quickcommands/utils/KillBackgroundProcessesPermissionUtil.ktD Capp/src/main/java/com/weinuo/quickcommands/utils/LanguageManager.ktA @app/src/main/java/com/weinuo/quickcommands/utils/LocaleHelper.ktK Japp/src/main/java/com/weinuo/quickcommands/utils/LocationPermissionUtil.ktH Gapp/src/main/java/com/weinuo/quickcommands/utils/MediaPermissionUtil.ktJ Iapp/src/main/java/com/weinuo/quickcommands/utils/NetworkPermissionUtil.ktG Fapp/src/main/java/com/weinuo/quickcommands/utils/NotificationHelper.ktO Napp/src/main/java/com/weinuo/quickcommands/utils/NotificationPermissionUtil.ktJ Iapp/src/main/java/com/weinuo/quickcommands/utils/OverlayPermissionUtil.ktC Bapp/src/main/java/com/weinuo/quickcommands/utils/RingtoneHelper.ktI Happ/src/main/java/com/weinuo/quickcommands/utils/SensorPermissionUtil.ktJ Iapp/src/main/java/com/weinuo/quickcommands/utils/StoragePermissionUtil.ktL Kapp/src/main/java/com/weinuo/quickcommands/utils/SunriseSunsetCalculator.ktM Lapp/src/main/java/com/weinuo/quickcommands/utils/UsageStatsPermissionUtil.ktE Dapp/src/main/java/com/weinuo/quickcommands/utils/VibrationManager.ktN Mapp/src/main/java/com/weinuo/quickcommands/viewmodel/PhoneCheckupViewModel.ktL Kapp/src/main/java/com/weinuo/quickcommands/widget/OneClickCommandWidget1.ktL Kapp/src/main/java/com/weinuo/quickcommands/widget/OneClickCommandWidget2.ktL Kapp/src/main/java/com/weinuo/quickcommands/widget/OneClickCommandWidget3.ktL Kapp/src/main/java/com/weinuo/quickcommands/widget/OneClickCommandWidget4.ktP Oapp/src/main/java/com/weinuo/quickcommands/widget/WidgetClickHandlerActivity.ktI Happ/src/main/java/com/weinuo/quickcommands/widget/WidgetUpdateManager.kt; :app/src/main/java/com/weinuo/quickcommands/MainActivity.ktS Rapp/src/main/java/com/weinuo/quickcommands/ui/screens/DetailConfigurationScreen.ktY Xapp/src/main/java/com/weinuo/quickcommands/ui/configuration/ConfigurationDataProvider.kt[ Zapp/src/main/java/com/weinuo/quickcommands/ui/components/NonExpandableConfigurationCard.ktS Rapp/src/main/java/com/weinuo/quickcommands/ui/screens/DetailConfigurationScreen.ktS Rapp/src/main/java/com/weinuo/quickcommands/ui/screens/DetailConfigurationScreen.ktU Tapp/src/main/java/com/weinuo/quickcommands/ui/screens/DetailedConfigurationScreen.kt\ [app/src/main/java/com/weinuo/quickcommands/ui/screens/UniversalDetailConfigurationScreen.kt\ [app/src/main/java/com/weinuo/quickcommands/ui/screens/UniversalDetailConfigurationScreen.kt\ [app/src/main/java/com/weinuo/quickcommands/ui/screens/UniversalDetailConfigurationScreen.ktV Uapp/src/main/java/com/weinuo/quickcommands/ui/activities/ItemConfigurationActivity.kt