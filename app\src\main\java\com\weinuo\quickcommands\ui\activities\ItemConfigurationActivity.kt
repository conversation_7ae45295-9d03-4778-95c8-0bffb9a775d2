package com.weinuo.quickcommands.ui.activities

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.selection.selectable
import androidx.compose.ui.unit.dp
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Check
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import com.weinuo.quickcommands.R
import com.weinuo.quickcommands.ui.theme.QuickCommandsTheme
import com.weinuo.quickcommands.ui.configuration.ConnectionStateConfigProvider
import com.weinuo.quickcommands.ui.configuration.CommunicationStateConfigProvider
import com.weinuo.quickcommands.ui.configuration.PhoneTaskConfigProvider
import com.weinuo.quickcommands.ui.configuration.InformationTaskConfigProvider
import com.weinuo.quickcommands.ui.configuration.DeviceEventConfigProvider
import com.weinuo.quickcommands.ui.configuration.DeviceSettingsTaskConfigProvider
import com.weinuo.quickcommands.ui.configuration.ConnectivityTaskConfigProvider
import com.weinuo.quickcommands.ui.configuration.NotificationTaskConfigProvider
import com.weinuo.quickcommands.ui.configuration.AppStateConfigProvider
import com.weinuo.quickcommands.ui.configuration.BatteryStateConfigProvider
import com.weinuo.quickcommands.ui.configuration.SensorStateConfigProvider
import com.weinuo.quickcommands.ui.components.ConfigurationCardItem

/**
 * 单个配置项配置Activity
 *
 * 用于显示单个配置项的详细配置界面，替代原来的卡片展开方式。
 * 支持从ConfigurationCardItem加载配置内容。
 */
class ItemConfigurationActivity : ComponentActivity() {
    
    companion object {
        private const val EXTRA_ITEM_ID = "item_id"
        private const val EXTRA_ITEM_TITLE = "item_title"
        private const val EXTRA_INITIAL_CONFIG = "initial_config"
        private const val EXTRA_EDIT_INDEX = "edit_index"
        
        /**
         * 启动单个配置项配置Activity
         */
        fun startForConfiguration(
            context: Context,
            itemId: String,
            itemTitle: String,
            initialConfig: Any? = null,
            editIndex: Int? = null
        ) {
            val intent = Intent(context, ItemConfigurationActivity::class.java).apply {
                putExtra(EXTRA_ITEM_ID, itemId)
                putExtra(EXTRA_ITEM_TITLE, itemTitle)
                initialConfig?.let { putExtra(EXTRA_INITIAL_CONFIG, it.toString()) }
                editIndex?.let { putExtra(EXTRA_EDIT_INDEX, it) }
            }
            context.startActivity(intent)
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 获取传递的参数
        val itemId = intent.getStringExtra(EXTRA_ITEM_ID) ?: ""
        val itemTitle = intent.getStringExtra(EXTRA_ITEM_TITLE) ?: ""
        val initialConfig = intent.getStringExtra(EXTRA_INITIAL_CONFIG)
        val editIndex = intent.getIntExtra(EXTRA_EDIT_INDEX, -1).takeIf { it != -1 }
        
        setContent {
            QuickCommandsTheme {
                ItemConfigurationActivityContent(
                    itemId = itemId,
                    itemTitle = itemTitle,
                    initialConfig = initialConfig,
                    editIndex = editIndex,
                    onFinish = { finish() },
                    onConfigurationComplete = { result ->
                        // 配置完成，返回结果
                        val resultIntent = Intent().apply {
                            putExtra("configuration_result", result.toString())
                            putExtra("item_id", itemId)
                            editIndex?.let { putExtra("edit_index", it) }
                        }
                        setResult(Activity.RESULT_OK, resultIntent)
                        finish()
                    }
                )
            }
        }
    }
}

/**
 * 单个配置项配置Activity的内容组件
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ItemConfigurationActivityContent(
    itemId: String,
    itemTitle: String,
    initialConfig: String?,
    editIndex: Int?,
    onFinish: () -> Unit,
    onConfigurationComplete: (Any) -> Unit
) {
    val context = LocalContext.current
    var configurationResult by remember { mutableStateOf<Any?>(null) }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(itemTitle) },
                navigationIcon = {
                    IconButton(onClick = onFinish) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = stringResource(R.string.back)
                        )
                    }
                },
                actions = {
                    // 保存按钮
                    IconButton(
                        onClick = {
                            configurationResult?.let { result ->
                                onConfigurationComplete(result)
                            }
                        },
                        enabled = configurationResult != null
                    ) {
                        Icon(
                            imageVector = Icons.Default.Check,
                            contentDescription = "保存"
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // 根据itemId加载对应的配置内容组件
            ItemConfigurationContent(
                itemId = itemId,
                initialConfig = initialConfig,
                editIndex = editIndex,
                onConfigurationComplete = { result ->
                    configurationResult = result
                }
            )
        }
    }
}

/**
 * 单个配置项内容组件
 *
 * 根据itemId动态加载对应的配置内容组件。
 */
@Composable
private fun ItemConfigurationContent(
    itemId: String,
    initialConfig: String?,
    editIndex: Int?,
    onConfigurationComplete: (Any) -> Unit
) {
    val context = LocalContext.current

    // 根据itemId查找对应的ConfigurationCardItem并显示其内容
    val configItem = remember(itemId) {
        findConfigurationCardItem(context, itemId)
    }

    if (configItem != null) {
        // 找到了对应的配置项，根据itemId生成自定义UI
        when (itemId) {
            // 信息任务
            "send_sms" -> SendSmsConfigurationUI(initialConfig, onConfigurationComplete)
            "send_email" -> SendEmailConfigurationUI(initialConfig, onConfigurationComplete)
            "message_ringtone" -> MessageRingtoneConfigurationUI(initialConfig, onConfigurationComplete)

            // 电话任务
            "open_call_log" -> OpenCallLogConfigurationUI(initialConfig, onConfigurationComplete)
            "make_call" -> MakeCallConfigurationUI(initialConfig, onConfigurationComplete)
            "answer_call" -> AnswerCallConfigurationUI(initialConfig, onConfigurationComplete)
            "clear_call_log" -> ClearCallLogConfigurationUI(initialConfig, onConfigurationComplete)

            // 连接状态
            "wifi_state" -> WifiStateConfigurationUI(initialConfig, onConfigurationComplete)
            "wifi_network" -> WifiNetworkConfigurationUI(initialConfig, onConfigurationComplete)
            "mobile_data" -> MobileDataConfigurationUI(initialConfig, onConfigurationComplete)
            "bluetooth_state" -> BluetoothStateConfigurationUI(initialConfig, onConfigurationComplete)
            "bluetooth_device" -> BluetoothDeviceConfigurationUI(initialConfig, onConfigurationComplete)
            "nfc_state" -> NfcStateConfigurationUI(initialConfig, onConfigurationComplete)
            "airplane_mode" -> AirplaneModeConfigurationUI(initialConfig, onConfigurationComplete)
            "hotspot_state" -> HotspotStateConfigurationUI(initialConfig, onConfigurationComplete)
            "vpn_state" -> VpnStateConfigurationUI(initialConfig, onConfigurationComplete)
            "ethernet_state" -> EthernetStateConfigurationUI(initialConfig, onConfigurationComplete)
            "cell_tower" -> CellTowerConfigurationUI(initialConfig, onConfigurationComplete)
            "mobile_signal" -> MobileSignalConfigurationUI(initialConfig, onConfigurationComplete)

            // 通信状态
            "sms_received" -> SmsReceivedConfigurationUI(initialConfig, onConfigurationComplete)
            "call_received" -> CallReceivedConfigurationUI(initialConfig, onConfigurationComplete)
            "call_ended" -> CallEndedConfigurationUI(initialConfig, onConfigurationComplete)
            "call_missed" -> CallMissedConfigurationUI(initialConfig, onConfigurationComplete)
            "call_outgoing" -> CallOutgoingConfigurationUI(initialConfig, onConfigurationComplete)

            // 设备事件
            "gps_state" -> GpsStateConfigurationUI(initialConfig, onConfigurationComplete)
            "screen_state" -> ScreenStateConfigurationUI(initialConfig, onConfigurationComplete)
            "headphone_state" -> HeadphoneStateConfigurationUI(initialConfig, onConfigurationComplete)
            "charging_state" -> ChargingStateConfigurationUI(initialConfig, onConfigurationComplete)
            "dock_state" -> DockStateConfigurationUI(initialConfig, onConfigurationComplete)
            "ringer_mode_changed" -> RingerModeConfigurationUI(initialConfig, onConfigurationComplete)
            "music_playback_state" -> MusicPlaybackConfigurationUI(initialConfig, onConfigurationComplete)
            "volume_changed" -> VolumeChangedConfigurationUI(initialConfig, onConfigurationComplete)
            "memory_state" -> MemoryStateConfigurationUI(initialConfig, onConfigurationComplete)

            // 电池状态
            "battery_level" -> BatteryLevelConfigurationUI(initialConfig, onConfigurationComplete)
            "battery_temperature" -> BatteryTemperatureConfigurationUI(initialConfig, onConfigurationComplete)
            "power_save_mode" -> PowerSaveModeConfigurationUI(initialConfig, onConfigurationComplete)

            // 传感器状态
            "light_sensor" -> LightSensorConfigurationUI(initialConfig, onConfigurationComplete)
            "proximity_sensor" -> ProximitySensorConfigurationUI(initialConfig, onConfigurationComplete)
            "accelerometer" -> AccelerometerConfigurationUI(initialConfig, onConfigurationComplete)
            "gyroscope" -> GyroscopeConfigurationUI(initialConfig, onConfigurationComplete)
            "magnetic_field" -> MagneticFieldConfigurationUI(initialConfig, onConfigurationComplete)
            "orientation" -> OrientationConfigurationUI(initialConfig, onConfigurationComplete)
            "pressure" -> PressureConfigurationUI(initialConfig, onConfigurationComplete)
            "temperature" -> TemperatureConfigurationUI(initialConfig, onConfigurationComplete)
            "humidity" -> HumidityConfigurationUI(initialConfig, onConfigurationComplete)

            // 应用状态
            "state_change" -> AppStateChangeConfigurationUI(initialConfig, onConfigurationComplete)
            "usage_stats" -> AppUsageStatsConfigurationUI(initialConfig, onConfigurationComplete)
            "notification_received" -> AppNotificationConfigurationUI(initialConfig, onConfigurationComplete)

            // 通知任务
            "show_notification" -> ShowNotificationConfigurationUI(initialConfig, onConfigurationComplete)
            "cancel_notification" -> CancelNotificationConfigurationUI(initialConfig, onConfigurationComplete)
            "show_dialog" -> ShowDialogConfigurationUI(initialConfig, onConfigurationComplete)
            "show_toast" -> ShowToastConfigurationUI(initialConfig, onConfigurationComplete)

            // 连接任务
            "wifi_control" -> WifiControlConfigurationUI(initialConfig, onConfigurationComplete)
            "bluetooth_control" -> BluetoothControlConfigurationUI(initialConfig, onConfigurationComplete)
            "mobile_data_control" -> MobileDataControlConfigurationUI(initialConfig, onConfigurationComplete)
            "nfc_control" -> NfcControlConfigurationUI(initialConfig, onConfigurationComplete)
            "airplane_mode_control" -> AirplaneModeControlConfigurationUI(initialConfig, onConfigurationComplete)
            "hotspot_control" -> HotspotControlConfigurationUI(initialConfig, onConfigurationComplete)
            "network_state_save" -> NetworkStateSaveConfigurationUI(initialConfig, onConfigurationComplete)
            "network_state_restore" -> NetworkStateRestoreConfigurationUI(initialConfig, onConfigurationComplete)

            // 设备设置任务
            "invert_colors" -> InvertColorsConfigurationUI(initialConfig, onConfigurationComplete)
            "auto_rotate" -> AutoRotateConfigurationUI(initialConfig, onConfigurationComplete)
            "stay_awake" -> StayAwakeConfigurationUI(initialConfig, onConfigurationComplete)
            "demo_mode" -> DemoModeConfigurationUI(initialConfig, onConfigurationComplete)
            "ambient_display" -> AmbientDisplayConfigurationUI(initialConfig, onConfigurationComplete)

            // 屏幕控制任务
            "brightness_control" -> BrightnessControlConfigurationUI(initialConfig, onConfigurationComplete)
            "screen_timeout" -> ScreenTimeoutConfigurationUI(initialConfig, onConfigurationComplete)
            "screen_orientation" -> ScreenOrientationConfigurationUI(initialConfig, onConfigurationComplete)
            "screen_on_off" -> ScreenOnOffConfigurationUI(initialConfig, onConfigurationComplete)
            "check_screen_text" -> CheckScreenTextConfigurationUI(initialConfig, onConfigurationComplete)
            "read_screen_content" -> ReadScreenContentConfigurationUI(initialConfig, onConfigurationComplete)

            else -> {
                // 其他配置项，使用通用配置UI
                GenericConfigurationUI(
                    configItem = configItem,
                    initialConfig = initialConfig,
                    onConfigurationComplete = onConfigurationComplete
                )
            }
        }
    } else {
        // 没有找到对应的配置项，显示占位内容
        PlaceholderConfigContent("未知配置项: $itemId", onConfigurationComplete)
    }
}

/**
 * 根据itemId查找对应的ConfigurationCardItem
 */
private fun findConfigurationCardItem(context: Context, itemId: String): ConfigurationCardItem<Any>? {
    // 从各个配置提供器中查找对应的配置项
    val allProviders = listOf(
        { ConnectionStateConfigProvider.getConfigurationItems(context) },
        { CommunicationStateConfigProvider.getConfigurationItems(context) },
        { PhoneTaskConfigProvider.getConfigurationItems(context) },
        { InformationTaskConfigProvider.getConfigurationItems(context) },
        { DeviceEventConfigProvider.getConfigurationItems(context) },
        { DeviceSettingsTaskConfigProvider.getConfigurationItems(context) },
        { ConnectivityTaskConfigProvider.getConfigurationItems(context) },
        { NotificationTaskConfigProvider.getConfigurationItems(context) },
        { AppStateConfigProvider.getConfigurationItems(context) },
        { BatteryStateConfigProvider.getConfigurationItems(context) },
        { SensorStateConfigProvider.getConfigurationItems(context) }
        // 可以添加更多配置提供器
    )

    for (provider in allProviders) {
        val items = provider()
        val item = items.find { it.id == itemId }
        if (item != null) {
            @Suppress("UNCHECKED_CAST")
            return item as ConfigurationCardItem<Any>
        }
    }

    return null
}



// 由于配置UI组件数量庞大，这里只实现几个关键的示例
// 其他配置UI将使用GenericConfigurationUI来处理

/**
 * 发送短信配置UI
 */
@Composable
private fun SendSmsConfigurationUI(
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit
) {
    var phoneNumber by remember { mutableStateOf("") }
    var messageText by remember { mutableStateOf("") }
    var selectedSimCard by remember { mutableStateOf("ASK_EACH_TIME") }
    var draftOnly by remember { mutableStateOf(false) }

    LaunchedEffect(phoneNumber, messageText, selectedSimCard, draftOnly) {
        if (phoneNumber.isNotBlank() && messageText.isNotBlank()) {
            val config = mapOf(
                "phoneNumber" to phoneNumber,
                "messageText" to messageText,
                "simCardSelection" to selectedSimCard,
                "draftOnly" to draftOnly
            )
            onConfigurationComplete(config)
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text("发送短信配置", style = MaterialTheme.typography.headlineSmall)

        OutlinedTextField(
            value = phoneNumber,
            onValueChange = { phoneNumber = it },
            label = { Text("电话号码") },
            modifier = Modifier.fillMaxWidth()
        )

        OutlinedTextField(
            value = messageText,
            onValueChange = { messageText = it },
            label = { Text("短信内容") },
            modifier = Modifier.fillMaxWidth(),
            minLines = 3
        )

        Text("SIM卡选择", style = MaterialTheme.typography.titleMedium)
        Column {
            listOf("SIM1" to "SIM1", "SIM2" to "SIM2", "每次询问" to "ASK_EACH_TIME").forEach { (label, value) ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = selectedSimCard == value,
                        onClick = { selectedSimCard = value }
                    )
                    Text(label)
                }
            }
        }

        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = draftOnly,
                onCheckedChange = { draftOnly = it }
            )
            Text("仅预填写不发送")
        }
    }
}

/**
 * 发送邮件配置UI
 */
@Composable
private fun SendEmailConfigurationUI(
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit
) {
    var emailRecipient by remember { mutableStateOf("") }
    var emailSubject by remember { mutableStateOf("") }
    var messageText by remember { mutableStateOf("") }

    // 监听配置变化，自动调用回调
    LaunchedEffect(emailRecipient, emailSubject, messageText) {
        if (emailRecipient.isNotBlank() && emailSubject.isNotBlank() && messageText.isNotBlank()) {
            val config = mapOf(
                "emailRecipient" to emailRecipient,
                "emailSubject" to emailSubject,
                "messageText" to messageText
            )
            onConfigurationComplete(config)
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "发送邮件配置",
            style = MaterialTheme.typography.headlineSmall
        )

        OutlinedTextField(
            value = emailRecipient,
            onValueChange = { emailRecipient = it },
            label = { Text("收件人邮箱") },
            modifier = Modifier.fillMaxWidth()
        )

        OutlinedTextField(
            value = emailSubject,
            onValueChange = { emailSubject = it },
            label = { Text("邮件主题") },
            modifier = Modifier.fillMaxWidth()
        )

        OutlinedTextField(
            value = messageText,
            onValueChange = { messageText = it },
            label = { Text("邮件内容") },
            modifier = Modifier.fillMaxWidth(),
            minLines = 5
        )
    }
}

/**
 * 信息铃声配置UI
 */
@Composable
private fun MessageRingtoneConfigurationUI(
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit
) {
    var selectedRingtone by remember { mutableStateOf("默认铃声") }

    // 监听配置变化，自动调用回调
    LaunchedEffect(selectedRingtone) {
        val config = mapOf(
            "selectedRingtone" to selectedRingtone
        )
        onConfigurationComplete(config)
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "信息铃声配置",
            style = MaterialTheme.typography.headlineSmall
        )

        OutlinedButton(
            onClick = { /* 打开铃声选择器 */ },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("选择铃声: $selectedRingtone")
        }
    }
}

/**
 * 打开通话记录配置UI
 */
@Composable
private fun OpenCallLogConfigurationUI(
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit
) {
    var filterType by remember { mutableStateOf("ALL") }

    // 监听配置变化，自动调用回调
    LaunchedEffect(filterType) {
        val config = mapOf(
            "filterType" to filterType
        )
        onConfigurationComplete(config)
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "打开通话记录配置",
            style = MaterialTheme.typography.headlineSmall
        )

        Text("筛选类型", style = MaterialTheme.typography.titleMedium)

        Column {
            listOf("全部" to "ALL", "未接来电" to "MISSED", "已接来电" to "INCOMING", "拨出电话" to "OUTGOING").forEach { (label, value) ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = filterType == value,
                        onClick = { filterType = value }
                    )
                    Text(label)
                }
            }
        }
    }
}

/**
 * WiFi状态配置UI
 */
@Composable
private fun WifiStateConfigurationUI(
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit
) {
    var wifiState by remember { mutableStateOf("CONNECTED") }
    var specificNetwork by remember { mutableStateOf("") }

    // 监听配置变化，自动调用回调
    LaunchedEffect(wifiState, specificNetwork) {
        val config = mapOf(
            "wifiState" to wifiState,
            "specificNetwork" to specificNetwork
        )
        onConfigurationComplete(config)
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "WiFi状态配置",
            style = MaterialTheme.typography.headlineSmall
        )

        Text("WiFi状态", style = MaterialTheme.typography.titleMedium)

        Column {
            listOf("已连接" to "CONNECTED", "已断开" to "DISCONNECTED", "正在连接" to "CONNECTING").forEach { (label, value) ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = wifiState == value,
                        onClick = { wifiState = value }
                    )
                    Text(label)
                }
            }
        }

        OutlinedTextField(
            value = specificNetwork,
            onValueChange = { specificNetwork = it },
            label = { Text("指定网络名称（可选）") },
            modifier = Modifier.fillMaxWidth()
        )
    }
}

/**
 * 通用配置UI
 *
 * 根据配置项ID和类型，生成对应的无按钮配置界面
 */
@Composable
private fun GenericConfigurationUI(
    configItem: ConfigurationCardItem<Any>,
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit
) {
    // 根据配置项的操作类型和ID，生成对应的配置界面
    when {
        // 连接状态相关配置
        configItem.id.contains("wifi") -> {
            WifiConfigurationUI(configItem, initialConfig, onConfigurationComplete)
        }
        configItem.id.contains("bluetooth") -> {
            BluetoothConfigurationUI(configItem, initialConfig, onConfigurationComplete)
        }
        configItem.id.contains("mobile") -> {
            MobileDataConfigurationUI(configItem, initialConfig, onConfigurationComplete)
        }

        // 通信状态相关配置
        configItem.id.contains("sms") -> {
            SmsConfigurationUI(configItem, initialConfig, onConfigurationComplete)
        }
        configItem.id.contains("call") -> {
            CallConfigurationUI(configItem, initialConfig, onConfigurationComplete)
        }

        // 设备事件相关配置
        configItem.id.contains("gps") -> {
            GpsConfigurationUI(configItem, initialConfig, onConfigurationComplete)
        }
        configItem.id.contains("battery") -> {
            BatteryConfigurationUI(configItem, initialConfig, onConfigurationComplete)
        }

        // 通知相关配置
        configItem.id.contains("notification") -> {
            NotificationConfigurationUI(configItem, initialConfig, onConfigurationComplete)
        }

        // 默认通用配置
        else -> {
            DefaultConfigurationUI(configItem, initialConfig, onConfigurationComplete)
        }
    }
}

/**
 * WiFi配置UI
 */
@Composable
private fun WifiConfigurationUI(
    configItem: ConfigurationCardItem<Any>,
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit
) {
    var wifiState by remember { mutableStateOf("CONNECTED") }
    var networkName by remember { mutableStateOf("") }
    var requireSpecificNetwork by remember { mutableStateOf(false) }

    LaunchedEffect(wifiState, networkName, requireSpecificNetwork) {
        val config = mapOf(
            "wifiState" to wifiState,
            "networkName" to networkName,
            "requireSpecificNetwork" to requireSpecificNetwork
        )
        onConfigurationComplete(config)
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = configItem.title,
            style = MaterialTheme.typography.headlineSmall
        )

        Text("WiFi状态", style = MaterialTheme.typography.titleMedium)
        Column {
            listOf("已连接" to "CONNECTED", "已断开" to "DISCONNECTED", "正在连接" to "CONNECTING").forEach { (label, value) ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = wifiState == value,
                        onClick = { wifiState = value }
                    )
                    Text(label)
                }
            }
        }

        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = requireSpecificNetwork,
                onCheckedChange = { requireSpecificNetwork = it }
            )
            Text("指定网络")
        }

        if (requireSpecificNetwork) {
            OutlinedTextField(
                value = networkName,
                onValueChange = { networkName = it },
                label = { Text("网络名称") },
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

/**
 * 蓝牙配置UI
 */
@Composable
private fun BluetoothConfigurationUI(
    configItem: ConfigurationCardItem<Any>,
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit
) {
    var bluetoothState by remember { mutableStateOf("CONNECTED") }
    var deviceName by remember { mutableStateOf("") }
    var requireSpecificDevice by remember { mutableStateOf(false) }

    LaunchedEffect(bluetoothState, deviceName, requireSpecificDevice) {
        val config = mapOf(
            "bluetoothState" to bluetoothState,
            "deviceName" to deviceName,
            "requireSpecificDevice" to requireSpecificDevice
        )
        onConfigurationComplete(config)
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = configItem.title,
            style = MaterialTheme.typography.headlineSmall
        )

        Text("蓝牙状态", style = MaterialTheme.typography.titleMedium)
        Column {
            listOf("已连接" to "CONNECTED", "已断开" to "DISCONNECTED", "正在连接" to "CONNECTING").forEach { (label, value) ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = bluetoothState == value,
                        onClick = { bluetoothState = value }
                    )
                    Text(label)
                }
            }
        }

        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = requireSpecificDevice,
                onCheckedChange = { requireSpecificDevice = it }
            )
            Text("指定设备")
        }

        if (requireSpecificDevice) {
            OutlinedTextField(
                value = deviceName,
                onValueChange = { deviceName = it },
                label = { Text("设备名称") },
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

/**
 * 移动数据配置UI
 */
@Composable
private fun MobileDataConfigurationUI(
    configItem: ConfigurationCardItem<Any>,
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit
) {
    var dataState by remember { mutableStateOf("ENABLED") }

    LaunchedEffect(dataState) {
        val config = mapOf(
            "dataState" to dataState
        )
        onConfigurationComplete(config)
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = configItem.title,
            style = MaterialTheme.typography.headlineSmall
        )

        Text("移动数据状态", style = MaterialTheme.typography.titleMedium)
        Column {
            listOf("已启用" to "ENABLED", "已禁用" to "DISABLED").forEach { (label, value) ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = dataState == value,
                        onClick = { dataState = value }
                    )
                    Text(label)
                }
            }
        }
    }
}

/**
 * 短信配置UI
 */
@Composable
private fun SmsConfigurationUI(
    configItem: ConfigurationCardItem<Any>,
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit
) {
    var smsType by remember { mutableStateOf("RECEIVED") }
    var senderNumber by remember { mutableStateOf("") }
    var messageContent by remember { mutableStateOf("") }
    var requireSpecificSender by remember { mutableStateOf(false) }
    var requireSpecificContent by remember { mutableStateOf(false) }

    LaunchedEffect(smsType, senderNumber, messageContent, requireSpecificSender, requireSpecificContent) {
        val config = mapOf(
            "smsType" to smsType,
            "senderNumber" to senderNumber,
            "messageContent" to messageContent,
            "requireSpecificSender" to requireSpecificSender,
            "requireSpecificContent" to requireSpecificContent
        )
        onConfigurationComplete(config)
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = configItem.title,
            style = MaterialTheme.typography.headlineSmall
        )

        Text("短信类型", style = MaterialTheme.typography.titleMedium)
        Column {
            listOf("收到短信" to "RECEIVED", "发送短信" to "SENT").forEach { (label, value) ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = smsType == value,
                        onClick = { smsType = value }
                    )
                    Text(label)
                }
            }
        }

        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = requireSpecificSender,
                onCheckedChange = { requireSpecificSender = it }
            )
            Text("指定发送方")
        }

        if (requireSpecificSender) {
            OutlinedTextField(
                value = senderNumber,
                onValueChange = { senderNumber = it },
                label = { Text("发送方号码") },
                modifier = Modifier.fillMaxWidth()
            )
        }

        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = requireSpecificContent,
                onCheckedChange = { requireSpecificContent = it }
            )
            Text("指定内容")
        }

        if (requireSpecificContent) {
            OutlinedTextField(
                value = messageContent,
                onValueChange = { messageContent = it },
                label = { Text("短信内容") },
                modifier = Modifier.fillMaxWidth(),
                minLines = 2
            )
        }
    }
}

/**
 * 通话配置UI
 */
@Composable
private fun CallConfigurationUI(
    configItem: ConfigurationCardItem<Any>,
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit
) {
    var callType by remember { mutableStateOf("INCOMING") }
    var phoneNumber by remember { mutableStateOf("") }
    var requireSpecificNumber by remember { mutableStateOf(false) }

    LaunchedEffect(callType, phoneNumber, requireSpecificNumber) {
        val config = mapOf(
            "callType" to callType,
            "phoneNumber" to phoneNumber,
            "requireSpecificNumber" to requireSpecificNumber
        )
        onConfigurationComplete(config)
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = configItem.title,
            style = MaterialTheme.typography.headlineSmall
        )

        Text("通话类型", style = MaterialTheme.typography.titleMedium)
        Column {
            listOf("来电" to "INCOMING", "拨出" to "OUTGOING", "未接" to "MISSED").forEach { (label, value) ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = callType == value,
                        onClick = { callType = value }
                    )
                    Text(label)
                }
            }
        }

        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = requireSpecificNumber,
                onCheckedChange = { requireSpecificNumber = it }
            )
            Text("指定号码")
        }

        if (requireSpecificNumber) {
            OutlinedTextField(
                value = phoneNumber,
                onValueChange = { phoneNumber = it },
                label = { Text("电话号码") },
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

/**
 * GPS配置UI
 */
@Composable
private fun GpsConfigurationUI(
    configItem: ConfigurationCardItem<Any>,
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit
) {
    var gpsState by remember { mutableStateOf("ENABLED") }

    LaunchedEffect(gpsState) {
        val config = mapOf(
            "gpsState" to gpsState
        )
        onConfigurationComplete(config)
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = configItem.title,
            style = MaterialTheme.typography.headlineSmall
        )

        Text("GPS状态", style = MaterialTheme.typography.titleMedium)
        Column {
            listOf("已启用" to "ENABLED", "已禁用" to "DISABLED").forEach { (label, value) ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = gpsState == value,
                        onClick = { gpsState = value }
                    )
                    Text(label)
                }
            }
        }
    }
}

/**
 * 电池配置UI
 */
@Composable
private fun BatteryConfigurationUI(
    configItem: ConfigurationCardItem<Any>,
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit
) {
    var batteryLevel by remember { mutableStateOf(50) }
    var batteryCondition by remember { mutableStateOf("ABOVE") }

    LaunchedEffect(batteryLevel, batteryCondition) {
        val config = mapOf(
            "batteryLevel" to batteryLevel,
            "batteryCondition" to batteryCondition
        )
        onConfigurationComplete(config)
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = configItem.title,
            style = MaterialTheme.typography.headlineSmall
        )

        Text("电池条件", style = MaterialTheme.typography.titleMedium)
        Column {
            listOf("高于" to "ABOVE", "低于" to "BELOW", "等于" to "EQUALS").forEach { (label, value) ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = batteryCondition == value,
                        onClick = { batteryCondition = value }
                    )
                    Text(label)
                }
            }
        }

        Text("电池电量: ${batteryLevel}%", style = MaterialTheme.typography.titleMedium)
        Slider(
            value = batteryLevel.toFloat(),
            onValueChange = { batteryLevel = it.toInt() },
            valueRange = 0f..100f,
            modifier = Modifier.fillMaxWidth()
        )
    }
}

/**
 * 通知配置UI
 */
@Composable
private fun NotificationConfigurationUI(
    configItem: ConfigurationCardItem<Any>,
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit
) {
    var notificationTitle by remember { mutableStateOf("") }
    var notificationText by remember { mutableStateOf("") }
    var notificationPriority by remember { mutableStateOf("NORMAL") }

    LaunchedEffect(notificationTitle, notificationText, notificationPriority) {
        if (notificationTitle.isNotBlank() && notificationText.isNotBlank()) {
            val config = mapOf(
                "notificationTitle" to notificationTitle,
                "notificationText" to notificationText,
                "notificationPriority" to notificationPriority
            )
            onConfigurationComplete(config)
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = configItem.title,
            style = MaterialTheme.typography.headlineSmall
        )

        OutlinedTextField(
            value = notificationTitle,
            onValueChange = { notificationTitle = it },
            label = { Text("通知标题") },
            modifier = Modifier.fillMaxWidth()
        )

        OutlinedTextField(
            value = notificationText,
            onValueChange = { notificationText = it },
            label = { Text("通知内容") },
            modifier = Modifier.fillMaxWidth(),
            minLines = 3
        )

        Text("通知优先级", style = MaterialTheme.typography.titleMedium)
        Column {
            listOf("低" to "LOW", "普通" to "NORMAL", "高" to "HIGH").forEach { (label, value) ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = notificationPriority == value,
                        onClick = { notificationPriority = value }
                    )
                    Text(label)
                }
            }
        }
    }
}

/**
 * 默认配置UI
 */
@Composable
private fun DefaultConfigurationUI(
    configItem: ConfigurationCardItem<Any>,
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit
) {
    var configValue by remember { mutableStateOf("") }

    LaunchedEffect(configValue) {
        if (configValue.isNotBlank()) {
            val config = mapOf(
                "itemId" to configItem.id,
                "title" to configItem.title,
                "value" to configValue
            )
            onConfigurationComplete(config)
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = configItem.title,
            style = MaterialTheme.typography.headlineSmall
        )

        Text(
            text = configItem.description,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        OutlinedTextField(
            value = configValue,
            onValueChange = { configValue = it },
            label = { Text("配置值") },
            modifier = Modifier.fillMaxWidth(),
            placeholder = { Text("请输入配置值") }
        )
    }
}

/**
 * 占位符配置内容组件
 */
@Composable
private fun PlaceholderConfigContent(
    title: String,
    onConfigurationComplete: (Any) -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            Text(
                text = "配置内容：$title\n\n这里将显示具体的配置选项。",
                style = MaterialTheme.typography.bodyLarge
            )
        }
    }
}
