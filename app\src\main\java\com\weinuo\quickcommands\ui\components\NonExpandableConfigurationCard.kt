package com.weinuo.quickcommands.ui.components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ChevronRight
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands.permission.PermissionRegistry
import com.weinuo.quickcommands.permission.GlobalPermissionManager

/**
 * 非展开式配置卡片
 *
 * 用于替代ExpandableConfigurationCard，点击后导航到新界面而不是展开内容。
 * 保持相同的权限检查逻辑，但移除展开/收起功能。
 *
 * @param T 操作类型的泛型参数
 * @param title 卡片标题
 * @param description 卡片描述
 * @param operationType 操作类型，用于权限检查
 * @param permissionRequired 是否需要权限检查，默认为true
 * @param onClick 点击回调，用于导航
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun <T : Any> NonExpandableConfigurationCard(
    title: String,
    description: String,
    operationType: T,
    permissionRequired: Boolean = true,
    onClick: () -> Unit,
    itemConfigLauncher: androidx.activity.result.ActivityResultLauncher<Intent>? = null,
    itemId: String? = null,
    initialConfig: Any? = null
) {
    val context = LocalContext.current

    // 权限检查状态
    var selectedOperation by remember { mutableStateOf<T?>(null) }

    // 获取全局权限管理器
    val globalPermissionManager = remember { GlobalPermissionManager.getInstance(context) }

    // 权限检查结果
    val hasPermission = if (permissionRequired) {
        try {
            val permission = PermissionRegistry.hasPermissionForOperation(operationType, globalPermissionManager, context)
            android.util.Log.d("NonExpandableCard", "权限检查结果: operationType=$operationType, hasPermission=$permission")
            permission
        } catch (e: Exception) {
            android.util.Log.e("NonExpandableCard", "权限检查失败: operationType=$operationType", e)
            true // 权限检查失败时默认认为有权限，让用户能够进入配置界面
        }
    } else {
        android.util.Log.d("NonExpandableCard", "不需要权限检查: operationType=$operationType")
        true
    }

    // 处理权限检查结果
    LaunchedEffect(selectedOperation) {
        selectedOperation?.let { operation ->
            android.util.Log.d("NonExpandableCard", "LaunchedEffect触发: operation=$operation, permissionRequired=$permissionRequired")
            if (permissionRequired) {
                // 使用PermissionRegistry进行权限检查
                android.util.Log.d("NonExpandableCard", "开始权限检查")
                PermissionRegistry.checkPermissionForOperation(
                    operation = operation,
                    globalPermissionManager = globalPermissionManager,
                    context = context
                )
                // 权限检查完成后执行点击回调
                android.util.Log.d("NonExpandableCard", "权限检查完成，执行点击回调")
                onClick()
                selectedOperation = null
            } else {
                // 不需要权限检查，直接执行点击回调
                android.util.Log.d("NonExpandableCard", "不需要权限检查，直接执行点击回调")
                onClick()
                selectedOperation = null
            }
        }
    }

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable {
                android.util.Log.d("NonExpandableCard", "卡片被点击: title=$title, permissionRequired=$permissionRequired, hasPermission=$hasPermission")
                if (permissionRequired && !hasPermission) {
                    // 需要权限但没有权限，触发权限检查
                    android.util.Log.d("NonExpandableCard", "需要权限检查，设置selectedOperation")
                    selectedOperation = operationType
                } else {
                    // 有权限或不需要权限，执行点击回调或启动配置Activity
                    android.util.Log.d("NonExpandableCard", "直接执行点击回调或启动配置Activity")
                    if (itemConfigLauncher != null && itemId != null) {
                        // 使用ActivityResultLauncher启动配置Activity
                        com.weinuo.quickcommands.ui.activities.ItemConfigurationActivity.startForConfigurationWithResult(
                            launcher = itemConfigLauncher,
                            context = context,
                            itemId = itemId,
                            itemTitle = title,
                            initialConfig = initialConfig
                        )
                    } else {
                        // 使用原有的点击回调
                        onClick()
                    }
                }
            },
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceContainerLow
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 2.dp
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 左侧内容
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                Spacer(modifier = Modifier.height(4.dp))
                
                Text(
                    text = description,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                
                // 权限状态提示
                if (permissionRequired && !hasPermission) {
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = "需要权限",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.error
                    )
                }
            }
            
            // 右侧箭头图标
            Icon(
                imageVector = Icons.Default.ChevronRight,
                contentDescription = "进入配置",
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}
