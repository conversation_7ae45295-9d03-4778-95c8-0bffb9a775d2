package com.weinuo.quickcommands.ui.activities

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.ui.unit.dp
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Check
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import com.weinuo.quickcommands.R
import com.weinuo.quickcommands.ui.theme.QuickCommandsTheme
import com.weinuo.quickcommands.ui.configuration.ConnectionStateConfigProvider
import com.weinuo.quickcommands.ui.configuration.CommunicationStateConfigProvider
import com.weinuo.quickcommands.ui.configuration.PhoneTaskConfigProvider
import com.weinuo.quickcommands.ui.configuration.InformationTaskConfigProvider
import com.weinuo.quickcommands.ui.configuration.DeviceEventConfigProvider
import com.weinuo.quickcommands.ui.configuration.DeviceSettingsTaskConfigProvider
import com.weinuo.quickcommands.ui.configuration.ConnectivityTaskConfigProvider
import com.weinuo.quickcommands.ui.configuration.NotificationTaskConfigProvider
import com.weinuo.quickcommands.ui.configuration.AppStateConfigProvider
import com.weinuo.quickcommands.ui.configuration.BatteryStateConfigProvider
import com.weinuo.quickcommands.ui.configuration.SensorStateConfigProvider
import com.weinuo.quickcommands.ui.components.ConfigurationCardItem
import com.weinuo.quickcommands.ui.theme.manager.ThemeManager
import com.weinuo.quickcommands.ui.theme.skyblue.SkyBlueStyleConfiguration
import com.weinuo.quickcommands.data.SettingsRepository
import com.weinuo.quickcommands.ui.components.themed.ThemedCard
import com.weinuo.quickcommands.ui.theme.manager.UISpacingConfigurationManager
import androidx.compose.runtime.mutableStateMapOf
import com.weinuo.quickcommands.storage.NavigationDataStorageManager

/**
 * 单个配置项配置Activity
 *
 * 用于显示单个配置项的详细配置界面，替代原来的卡片展开方式。
 * 支持从ConfigurationCardItem加载配置内容。
 */
class ItemConfigurationActivity : ComponentActivity() {
    
    companion object {
        private const val EXTRA_ITEM_ID = "item_id"
        private const val EXTRA_ITEM_TITLE = "item_title"
        private const val EXTRA_INITIAL_CONFIG = "initial_config"
        private const val EXTRA_EDIT_INDEX = "edit_index"
        
        /**
         * 启动单个配置项配置Activity（无返回结果）
         */
        fun startForConfiguration(
            context: Context,
            itemId: String,
            itemTitle: String,
            initialConfig: Any? = null,
            editIndex: Int? = null
        ) {
            val intent = Intent(context, ItemConfigurationActivity::class.java).apply {
                putExtra(EXTRA_ITEM_ID, itemId)
                putExtra(EXTRA_ITEM_TITLE, itemTitle)
                initialConfig?.let { putExtra(EXTRA_INITIAL_CONFIG, it.toString()) }
                editIndex?.let { putExtra(EXTRA_EDIT_INDEX, it) }
            }
            context.startActivity(intent)
        }

        /**
         * 启动单个配置项配置Activity（带返回结果）
         */
        fun startForConfigurationWithResult(
            launcher: androidx.activity.result.ActivityResultLauncher<Intent>,
            context: Context,
            itemId: String,
            itemTitle: String,
            initialConfig: Any? = null,
            editIndex: Int? = null
        ) {
            val intent = Intent(context, ItemConfigurationActivity::class.java).apply {
                putExtra(EXTRA_ITEM_ID, itemId)
                putExtra(EXTRA_ITEM_TITLE, itemTitle)
                initialConfig?.let { putExtra(EXTRA_INITIAL_CONFIG, it.toString()) }
                editIndex?.let { putExtra(EXTRA_EDIT_INDEX, it) }
            }
            launcher.launch(intent)
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 获取传递的参数
        val itemId = intent.getStringExtra(EXTRA_ITEM_ID) ?: ""
        val itemTitle = intent.getStringExtra(EXTRA_ITEM_TITLE) ?: ""
        val initialConfig = intent.getStringExtra(EXTRA_INITIAL_CONFIG)
        val editIndex = intent.getIntExtra(EXTRA_EDIT_INDEX, -1).takeIf { it != -1 }
        
        setContent {
            QuickCommandsTheme {
                ItemConfigurationActivityContent(
                    itemId = itemId,
                    itemTitle = itemTitle,
                    initialConfig = initialConfig,
                    editIndex = editIndex,
                    onFinish = { finish() },
                    onConfigurationComplete = { result ->
                        android.util.Log.d("ItemConfigurationActivity", "配置完成回调: result=$result")

                        // 配置完成，需要将结果转换为条件或任务对象并保存到NavigationDataStorageManager
                        try {
                            val navigationDataManager = NavigationDataStorageManager(this@ItemConfigurationActivity)
                            val navigationKey = "item_config_${System.currentTimeMillis()}"

                            // 根据配置结果创建条件或任务对象
                            // 这里需要根据itemId和result创建对应的SharedTriggerCondition或SharedTask
                            // 暂时使用简单数据存储
                            val saveResult = navigationDataManager.saveSimpleNavigationData(navigationKey, mapOf(
                                "item_id" to itemId,
                                "config_result" to result.toString(),
                                "edit_index" to (editIndex ?: -1)
                            ))

                            android.util.Log.d("ItemConfigurationActivity", "保存配置数据: navigationKey=$navigationKey, success=$saveResult")

                            val resultIntent = Intent().apply {
                                putExtra("navigation_key", navigationKey)
                                putExtra("item_id", itemId)
                                editIndex?.let { putExtra("edit_index", it) }
                            }
                            setResult(Activity.RESULT_OK, resultIntent)
                            finish()
                        } catch (e: Exception) {
                            android.util.Log.e("ItemConfigurationActivity", "保存配置数据失败", e)
                            // 即使保存失败，也返回结果
                            val resultIntent = Intent().apply {
                                putExtra("configuration_result", result.toString())
                                putExtra("item_id", itemId)
                                editIndex?.let { putExtra("edit_index", it) }
                            }
                            setResult(Activity.RESULT_OK, resultIntent)
                            finish()
                        }
                    }
                )
            }
        }
    }
}

/**
 * 单个配置项配置Activity的内容组件
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ItemConfigurationActivityContent(
    itemId: String,
    itemTitle: String,
    initialConfig: String?,
    editIndex: Int?,
    onFinish: () -> Unit,
    onConfigurationComplete: (Any) -> Unit
) {
    val context = LocalContext.current
    var configurationResult by remember { mutableStateOf<Any?>(null) }

    // 获取主题信息
    val themeManager = remember { ThemeManager.getInstance(context) }
    val currentThemeId = themeManager.getCurrentThemeId()

    // 根据主题选择不同的UI实现
    when (currentThemeId) {
        "ocean_blue" -> {
            // 海洋蓝主题：使用卡片形式，所有选项外露
            OceanBlueItemConfigurationContent(
                itemId = itemId,
                itemTitle = itemTitle,
                initialConfig = initialConfig,
                editIndex = editIndex,
                onFinish = onFinish,
                onConfigurationComplete = onConfigurationComplete
            )
        }
        "sky_blue" -> {
            // 天空蓝主题：只显示标题和当前状态，点击导航到新界面
            SkyBlueItemConfigurationContent(
                itemId = itemId,
                itemTitle = itemTitle,
                initialConfig = initialConfig,
                editIndex = editIndex,
                onFinish = onFinish,
                onConfigurationComplete = onConfigurationComplete
            )
        }
        else -> {
            // 默认实现（海洋蓝风格）
            OceanBlueItemConfigurationContent(
                itemId = itemId,
                itemTitle = itemTitle,
                initialConfig = initialConfig,
                editIndex = editIndex,
                onFinish = onFinish,
                onConfigurationComplete = onConfigurationComplete
            )
        }
    }
}

/**
 * 海洋蓝主题的配置界面实现
 *
 * 特点：卡片形式，所有标题和选项基本外露
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun OceanBlueItemConfigurationContent(
    itemId: String,
    itemTitle: String,
    initialConfig: String?,
    editIndex: Int?,
    onFinish: () -> Unit,
    onConfigurationComplete: (Any) -> Unit
) {
    val context = LocalContext.current
    var configurationResult by remember { mutableStateOf<Any?>(null) }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(itemTitle) },
                navigationIcon = {
                    IconButton(onClick = onFinish) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = stringResource(R.string.back)
                        )
                    }
                },
                actions = {
                    // 保存按钮
                    TextButton(
                        onClick = {
                            configurationResult?.let { result ->
                                android.util.Log.d("ItemConfigurationActivity", "保存配置: result=$result")
                                onConfigurationComplete(result)
                            } ?: run {
                                android.util.Log.w("ItemConfigurationActivity", "配置结果为空，无法保存")
                            }
                        },
                        enabled = configurationResult != null
                    ) {
                        Text("保存")
                    }
                }
            )
        }
    ) { paddingValues ->
        // 海洋蓝主题：使用卡片形式显示所有配置选项
        Card(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            shape = RoundedCornerShape(12.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
        ) {
            ItemConfigurationContent(
                itemId = itemId,
                initialConfig = initialConfig,
                editIndex = editIndex,
                onConfigurationComplete = { result ->
                    configurationResult = result
                }
            )
        }
    }
}

/**
 * 天空蓝主题的配置界面实现
 *
 * 特点：使用卡片和分割线，类似天空蓝全局设置的UI风格
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun SkyBlueItemConfigurationContent(
    itemId: String,
    itemTitle: String,
    initialConfig: String?,
    editIndex: Int?,
    onFinish: () -> Unit,
    onConfigurationComplete: (Any) -> Unit
) {
    val context = LocalContext.current
    var configurationResult by remember { mutableStateOf<Any?>(null) }

    // 获取动态卡片样式配置和UI间距配置
    val settingsRepository = remember { SettingsRepository(context) }
    val cardStyle = SkyBlueStyleConfiguration.getDynamicCardStyleConfig(settingsRepository)
    val uiSpacingConfigManager = remember { UISpacingConfigurationManager.getInstance(context, settingsRepository) }
    val uiSpacingConfig = uiSpacingConfigManager.getUISpacingConfiguration()

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(itemTitle) },
                navigationIcon = {
                    IconButton(onClick = onFinish) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = stringResource(R.string.back)
                        )
                    }
                },
                actions = {
                    // 保存按钮
                    TextButton(
                        onClick = {
                            configurationResult?.let { result ->
                                android.util.Log.d("ItemConfigurationActivity", "天空蓝保存配置: result=$result")
                                onConfigurationComplete(result)
                            } ?: run {
                                android.util.Log.w("ItemConfigurationActivity", "天空蓝配置结果为空，无法保存")
                            }
                        },
                        enabled = configurationResult != null
                    ) {
                        Text("保存")
                    }
                }
            )
        }
    ) { paddingValues ->
        // 天空蓝主题：使用卡片形式显示配置内容，类似全局设置的风格
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(horizontal = 16.dp),
            verticalArrangement = Arrangement.spacedBy(uiSpacingConfig.globalSettingsItemSpacing.dp)
        ) {
            item {
                ThemedCard(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    SkyBlueItemConfigurationContent(
                        itemId = itemId,
                        initialConfig = initialConfig,
                        editIndex = editIndex,
                        onConfigurationComplete = { result ->
                            configurationResult = result
                        },
                        uiSpacingConfig = uiSpacingConfig
                    )
                }
            }
        }
    }
}

/**
 * 天空蓝主题的配置内容组件
 *
 * 在卡片内部显示配置选项，使用分割线分隔
 */
@Composable
private fun SkyBlueItemConfigurationContent(
    itemId: String,
    initialConfig: String?,
    editIndex: Int?,
    onConfigurationComplete: (Any) -> Unit,
    uiSpacingConfig: UISpacingConfigurationManager.UISpacingConfiguration
) {
    val context = LocalContext.current

    // 根据itemId查找对应的ConfigurationCardItem并显示其内容
    val configItem = remember(itemId) {
        findConfigurationCardItem(context, itemId)
    }

    if (configItem != null) {
        // 找到了对应的配置项，根据itemId生成自定义UI
        when (itemId) {
            // 只为几个关键配置项实现专门的UI，其他使用通用UI
            "send_sms" -> SkyBlueSendSmsConfigurationUI(initialConfig, onConfigurationComplete, uiSpacingConfig)
            "send_email" -> SkyBlueSendEmailConfigurationUI(initialConfig, onConfigurationComplete, uiSpacingConfig)
            "open_call_log" -> SkyBlueOpenCallLogConfigurationUI(initialConfig, onConfigurationComplete, uiSpacingConfig)
            "wifi_state" -> SkyBlueWifiStateConfigurationUI(initialConfig, onConfigurationComplete, uiSpacingConfig)
            "ip_address" -> SkyBlueIpAddressConfigurationUI(initialConfig, onConfigurationComplete, uiSpacingConfig)

            // 其他所有配置项都使用通用配置UI
            else -> SkyBlueGenericConfigurationUI(configItem, initialConfig, onConfigurationComplete, uiSpacingConfig)
        }
    } else {
        // 没有找到对应的配置项，显示占位内容
        SkyBluePlaceholderConfigContent("未知配置项: $itemId", onConfigurationComplete, uiSpacingConfig)
    }
}

/**
 * 单个配置项内容组件
 *
 * 根据itemId动态加载对应的配置内容组件。
 */
@Composable
private fun ItemConfigurationContent(
    itemId: String,
    initialConfig: String?,
    editIndex: Int?,
    onConfigurationComplete: (Any) -> Unit
) {
    val context = LocalContext.current

    // 根据itemId查找对应的ConfigurationCardItem并显示其内容
    val configItem = remember(itemId) {
        findConfigurationCardItem(context, itemId)
    }

    if (configItem != null) {
        // 找到了对应的配置项，根据itemId生成自定义UI
        when (itemId) {
            // 只为几个关键配置项实现专门的UI，其他使用通用UI
            "send_sms" -> SendSmsConfigurationUI(initialConfig, onConfigurationComplete)
            "send_email" -> SendEmailConfigurationUI(initialConfig, onConfigurationComplete)
            "open_call_log" -> OpenCallLogConfigurationUI(initialConfig, onConfigurationComplete)
            "wifi_state" -> WifiStateConfigurationUI(initialConfig, onConfigurationComplete)
            "ip_address" -> IpAddressConfigurationUI(initialConfig, onConfigurationComplete)

            // 其他所有配置项都使用通用配置UI
            else -> GenericConfigurationUI(configItem, initialConfig, onConfigurationComplete)
        }
    } else {
        // 没有找到对应的配置项，显示占位内容
        PlaceholderConfigContent("未知配置项: $itemId", onConfigurationComplete)
    }
}

/**
 * 根据itemId查找对应的ConfigurationCardItem
 */
private fun findConfigurationCardItem(context: Context, itemId: String): ConfigurationCardItem<Any>? {
    // 从各个配置提供器中查找对应的配置项
    val allProviders = listOf(
        { ConnectionStateConfigProvider.getConfigurationItems(context) },
        { CommunicationStateConfigProvider.getConfigurationItems(context) },
        { PhoneTaskConfigProvider.getConfigurationItems(context) },
        { InformationTaskConfigProvider.getConfigurationItems(context) },
        { DeviceEventConfigProvider.getConfigurationItems(context) },
        { DeviceSettingsTaskConfigProvider.getConfigurationItems(context) },
        { ConnectivityTaskConfigProvider.getConfigurationItems(context) },
        { NotificationTaskConfigProvider.getConfigurationItems(context) },
        { AppStateConfigProvider.getConfigurationItems(context) },
        { BatteryStateConfigProvider.getConfigurationItems(context) },
        { SensorStateConfigProvider.getConfigurationItems(context) }
        // 可以添加更多配置提供器
    )

    for (provider in allProviders) {
        val items = provider()
        val item = items.find { it.id == itemId }
        if (item != null) {
            @Suppress("UNCHECKED_CAST")
            return item as ConfigurationCardItem<Any>
        }
    }

    return null
}



// 由于配置UI组件数量庞大，这里只实现几个关键的示例
// 其他配置UI将使用GenericConfigurationUI来处理

/**
 * 发送短信配置UI
 */
@Composable
private fun SendSmsConfigurationUI(
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit
) {
    var phoneNumber by remember { mutableStateOf("") }
    var messageText by remember { mutableStateOf("") }
    var selectedSimCard by remember { mutableStateOf("ASK_EACH_TIME") }
    var draftOnly by remember { mutableStateOf(false) }

    LaunchedEffect(phoneNumber, messageText, selectedSimCard, draftOnly) {
        if (phoneNumber.isNotBlank() && messageText.isNotBlank()) {
            val config = mapOf(
                "phoneNumber" to phoneNumber,
                "messageText" to messageText,
                "simCardSelection" to selectedSimCard,
                "draftOnly" to draftOnly
            )
            onConfigurationComplete(config)
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text("发送短信配置", style = MaterialTheme.typography.headlineSmall)

        OutlinedTextField(
            value = phoneNumber,
            onValueChange = { phoneNumber = it },
            label = { Text("电话号码") },
            modifier = Modifier.fillMaxWidth()
        )

        OutlinedTextField(
            value = messageText,
            onValueChange = { messageText = it },
            label = { Text("短信内容") },
            modifier = Modifier.fillMaxWidth(),
            minLines = 3
        )

        Text("SIM卡选择", style = MaterialTheme.typography.titleMedium)
        Column {
            listOf("SIM1" to "SIM1", "SIM2" to "SIM2", "每次询问" to "ASK_EACH_TIME").forEach { (label, value) ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = selectedSimCard == value,
                        onClick = { selectedSimCard = value }
                    )
                    Text(label)
                }
            }
        }

        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = draftOnly,
                onCheckedChange = { draftOnly = it }
            )
            Text("仅预填写不发送")
        }
    }
}

/**
 * 发送邮件配置UI
 */
@Composable
private fun SendEmailConfigurationUI(
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit
) {
    var emailRecipient by remember { mutableStateOf("") }
    var emailSubject by remember { mutableStateOf("") }
    var messageText by remember { mutableStateOf("") }

    // 监听配置变化，自动调用回调
    LaunchedEffect(emailRecipient, emailSubject, messageText) {
        if (emailRecipient.isNotBlank() && emailSubject.isNotBlank() && messageText.isNotBlank()) {
            val config = mapOf(
                "emailRecipient" to emailRecipient,
                "emailSubject" to emailSubject,
                "messageText" to messageText
            )
            onConfigurationComplete(config)
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "发送邮件配置",
            style = MaterialTheme.typography.headlineSmall
        )

        OutlinedTextField(
            value = emailRecipient,
            onValueChange = { emailRecipient = it },
            label = { Text("收件人邮箱") },
            modifier = Modifier.fillMaxWidth()
        )

        OutlinedTextField(
            value = emailSubject,
            onValueChange = { emailSubject = it },
            label = { Text("邮件主题") },
            modifier = Modifier.fillMaxWidth()
        )

        OutlinedTextField(
            value = messageText,
            onValueChange = { messageText = it },
            label = { Text("邮件内容") },
            modifier = Modifier.fillMaxWidth(),
            minLines = 5
        )
    }
}

/**
 * 信息铃声配置UI
 */
@Composable
private fun MessageRingtoneConfigurationUI(
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit
) {
    var selectedRingtone by remember { mutableStateOf("默认铃声") }

    // 监听配置变化，自动调用回调
    LaunchedEffect(selectedRingtone) {
        val config = mapOf(
            "selectedRingtone" to selectedRingtone
        )
        onConfigurationComplete(config)
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "信息铃声配置",
            style = MaterialTheme.typography.headlineSmall
        )

        OutlinedButton(
            onClick = { /* 打开铃声选择器 */ },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("选择铃声: $selectedRingtone")
        }
    }
}

/**
 * 打开通话记录配置UI
 */
@Composable
private fun OpenCallLogConfigurationUI(
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit
) {
    var filterType by remember { mutableStateOf("ALL") }

    // 监听配置变化，自动调用回调
    LaunchedEffect(filterType) {
        val config = mapOf(
            "filterType" to filterType
        )
        onConfigurationComplete(config)
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "打开通话记录配置",
            style = MaterialTheme.typography.headlineSmall
        )

        Text("筛选类型", style = MaterialTheme.typography.titleMedium)

        Column {
            listOf("全部" to "ALL", "未接来电" to "MISSED", "已接来电" to "INCOMING", "拨出电话" to "OUTGOING").forEach { (label, value) ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = filterType == value,
                        onClick = { filterType = value }
                    )
                    Text(label)
                }
            }
        }
    }
}

/**
 * WiFi状态配置UI
 */
@Composable
private fun WifiStateConfigurationUI(
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit
) {
    var wifiState by remember { mutableStateOf("CONNECTED") }
    var specificNetwork by remember { mutableStateOf("") }

    // 监听配置变化，自动调用回调
    LaunchedEffect(wifiState, specificNetwork) {
        val config = mapOf(
            "wifiState" to wifiState,
            "specificNetwork" to specificNetwork
        )
        onConfigurationComplete(config)
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "WiFi状态配置",
            style = MaterialTheme.typography.headlineSmall
        )

        Text("WiFi状态", style = MaterialTheme.typography.titleMedium)

        Column {
            listOf("已连接" to "CONNECTED", "已断开" to "DISCONNECTED", "正在连接" to "CONNECTING").forEach { (label, value) ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = wifiState == value,
                        onClick = { wifiState = value }
                    )
                    Text(label)
                }
            }
        }

        OutlinedTextField(
            value = specificNetwork,
            onValueChange = { specificNetwork = it },
            label = { Text("指定网络名称（可选）") },
            modifier = Modifier.fillMaxWidth()
        )
    }
}

/**
 * 通用配置UI
 *
 * 根据配置项ID和类型，生成对应的无按钮配置界面
 */
@Composable
private fun GenericConfigurationUI(
    configItem: ConfigurationCardItem<Any>,
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit
) {
    // 根据配置项的操作类型和ID，生成对应的配置界面
    when {
        // 连接状态相关配置
        configItem.id.contains("wifi") -> {
            WifiConfigurationUI(configItem, initialConfig, onConfigurationComplete)
        }
        configItem.id.contains("bluetooth") -> {
            BluetoothConfigurationUI(configItem, initialConfig, onConfigurationComplete)
        }
        configItem.id.contains("mobile") -> {
            MobileDataConfigurationUI(configItem, initialConfig, onConfigurationComplete)
        }

        // 通信状态相关配置
        configItem.id.contains("sms") -> {
            SmsConfigurationUI(configItem, initialConfig, onConfigurationComplete)
        }
        configItem.id.contains("call") -> {
            CallConfigurationUI(configItem, initialConfig, onConfigurationComplete)
        }

        // 设备事件相关配置
        configItem.id.contains("gps") -> {
            GpsConfigurationUI(configItem, initialConfig, onConfigurationComplete)
        }
        configItem.id.contains("battery") -> {
            BatteryConfigurationUI(configItem, initialConfig, onConfigurationComplete)
        }

        // 通知相关配置
        configItem.id.contains("notification") -> {
            NotificationConfigurationUI(configItem, initialConfig, onConfigurationComplete)
        }

        // 默认通用配置
        else -> {
            DefaultConfigurationUI(configItem, initialConfig, onConfigurationComplete)
        }
    }
}

/**
 * WiFi配置UI
 */
@Composable
private fun WifiConfigurationUI(
    configItem: ConfigurationCardItem<Any>,
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit
) {
    var wifiState by remember { mutableStateOf("CONNECTED") }
    var networkName by remember { mutableStateOf("") }
    var requireSpecificNetwork by remember { mutableStateOf(false) }

    LaunchedEffect(wifiState, networkName, requireSpecificNetwork) {
        val config = mapOf(
            "wifiState" to wifiState,
            "networkName" to networkName,
            "requireSpecificNetwork" to requireSpecificNetwork
        )
        onConfigurationComplete(config)
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = configItem.title,
            style = MaterialTheme.typography.headlineSmall
        )

        Text("WiFi状态", style = MaterialTheme.typography.titleMedium)
        Column {
            listOf("已连接" to "CONNECTED", "已断开" to "DISCONNECTED", "正在连接" to "CONNECTING").forEach { (label, value) ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = wifiState == value,
                        onClick = { wifiState = value }
                    )
                    Text(label)
                }
            }
        }

        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = requireSpecificNetwork,
                onCheckedChange = { requireSpecificNetwork = it }
            )
            Text("指定网络")
        }

        if (requireSpecificNetwork) {
            OutlinedTextField(
                value = networkName,
                onValueChange = { networkName = it },
                label = { Text("网络名称") },
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

/**
 * 蓝牙配置UI
 */
@Composable
private fun BluetoothConfigurationUI(
    configItem: ConfigurationCardItem<Any>,
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit
) {
    var bluetoothState by remember { mutableStateOf("CONNECTED") }
    var deviceName by remember { mutableStateOf("") }
    var requireSpecificDevice by remember { mutableStateOf(false) }

    LaunchedEffect(bluetoothState, deviceName, requireSpecificDevice) {
        val config = mapOf(
            "bluetoothState" to bluetoothState,
            "deviceName" to deviceName,
            "requireSpecificDevice" to requireSpecificDevice
        )
        onConfigurationComplete(config)
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = configItem.title,
            style = MaterialTheme.typography.headlineSmall
        )

        Text("蓝牙状态", style = MaterialTheme.typography.titleMedium)
        Column {
            listOf("已连接" to "CONNECTED", "已断开" to "DISCONNECTED", "正在连接" to "CONNECTING").forEach { (label, value) ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = bluetoothState == value,
                        onClick = { bluetoothState = value }
                    )
                    Text(label)
                }
            }
        }

        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = requireSpecificDevice,
                onCheckedChange = { requireSpecificDevice = it }
            )
            Text("指定设备")
        }

        if (requireSpecificDevice) {
            OutlinedTextField(
                value = deviceName,
                onValueChange = { deviceName = it },
                label = { Text("设备名称") },
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

/**
 * 移动数据配置UI
 */
@Composable
private fun MobileDataConfigurationUI(
    configItem: ConfigurationCardItem<Any>,
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit
) {
    var dataState by remember { mutableStateOf("ENABLED") }

    LaunchedEffect(dataState) {
        val config = mapOf(
            "dataState" to dataState
        )
        onConfigurationComplete(config)
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = configItem.title,
            style = MaterialTheme.typography.headlineSmall
        )

        Text("移动数据状态", style = MaterialTheme.typography.titleMedium)
        Column {
            listOf("已启用" to "ENABLED", "已禁用" to "DISABLED").forEach { (label, value) ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = dataState == value,
                        onClick = { dataState = value }
                    )
                    Text(label)
                }
            }
        }
    }
}

/**
 * 短信配置UI
 */
@Composable
private fun SmsConfigurationUI(
    configItem: ConfigurationCardItem<Any>,
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit
) {
    var smsType by remember { mutableStateOf("RECEIVED") }
    var senderNumber by remember { mutableStateOf("") }
    var messageContent by remember { mutableStateOf("") }
    var requireSpecificSender by remember { mutableStateOf(false) }
    var requireSpecificContent by remember { mutableStateOf(false) }

    LaunchedEffect(smsType, senderNumber, messageContent, requireSpecificSender, requireSpecificContent) {
        val config = mapOf(
            "smsType" to smsType,
            "senderNumber" to senderNumber,
            "messageContent" to messageContent,
            "requireSpecificSender" to requireSpecificSender,
            "requireSpecificContent" to requireSpecificContent
        )
        onConfigurationComplete(config)
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = configItem.title,
            style = MaterialTheme.typography.headlineSmall
        )

        Text("短信类型", style = MaterialTheme.typography.titleMedium)
        Column {
            listOf("收到短信" to "RECEIVED", "发送短信" to "SENT").forEach { (label, value) ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = smsType == value,
                        onClick = { smsType = value }
                    )
                    Text(label)
                }
            }
        }

        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = requireSpecificSender,
                onCheckedChange = { requireSpecificSender = it }
            )
            Text("指定发送方")
        }

        if (requireSpecificSender) {
            OutlinedTextField(
                value = senderNumber,
                onValueChange = { senderNumber = it },
                label = { Text("发送方号码") },
                modifier = Modifier.fillMaxWidth()
            )
        }

        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = requireSpecificContent,
                onCheckedChange = { requireSpecificContent = it }
            )
            Text("指定内容")
        }

        if (requireSpecificContent) {
            OutlinedTextField(
                value = messageContent,
                onValueChange = { messageContent = it },
                label = { Text("短信内容") },
                modifier = Modifier.fillMaxWidth(),
                minLines = 2
            )
        }
    }
}

/**
 * 通话配置UI
 */
@Composable
private fun CallConfigurationUI(
    configItem: ConfigurationCardItem<Any>,
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit
) {
    var callType by remember { mutableStateOf("INCOMING") }
    var phoneNumber by remember { mutableStateOf("") }
    var requireSpecificNumber by remember { mutableStateOf(false) }

    LaunchedEffect(callType, phoneNumber, requireSpecificNumber) {
        val config = mapOf(
            "callType" to callType,
            "phoneNumber" to phoneNumber,
            "requireSpecificNumber" to requireSpecificNumber
        )
        onConfigurationComplete(config)
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = configItem.title,
            style = MaterialTheme.typography.headlineSmall
        )

        Text("通话类型", style = MaterialTheme.typography.titleMedium)
        Column {
            listOf("来电" to "INCOMING", "拨出" to "OUTGOING", "未接" to "MISSED").forEach { (label, value) ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = callType == value,
                        onClick = { callType = value }
                    )
                    Text(label)
                }
            }
        }

        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = requireSpecificNumber,
                onCheckedChange = { requireSpecificNumber = it }
            )
            Text("指定号码")
        }

        if (requireSpecificNumber) {
            OutlinedTextField(
                value = phoneNumber,
                onValueChange = { phoneNumber = it },
                label = { Text("电话号码") },
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

/**
 * GPS配置UI
 */
@Composable
private fun GpsConfigurationUI(
    configItem: ConfigurationCardItem<Any>,
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit
) {
    var gpsState by remember { mutableStateOf("ENABLED") }

    LaunchedEffect(gpsState) {
        val config = mapOf(
            "gpsState" to gpsState
        )
        onConfigurationComplete(config)
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = configItem.title,
            style = MaterialTheme.typography.headlineSmall
        )

        Text("GPS状态", style = MaterialTheme.typography.titleMedium)
        Column {
            listOf("已启用" to "ENABLED", "已禁用" to "DISABLED").forEach { (label, value) ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = gpsState == value,
                        onClick = { gpsState = value }
                    )
                    Text(label)
                }
            }
        }
    }
}

/**
 * 电池配置UI
 */
@Composable
private fun BatteryConfigurationUI(
    configItem: ConfigurationCardItem<Any>,
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit
) {
    var batteryLevel by remember { mutableStateOf(50) }
    var batteryCondition by remember { mutableStateOf("ABOVE") }

    LaunchedEffect(batteryLevel, batteryCondition) {
        val config = mapOf(
            "batteryLevel" to batteryLevel,
            "batteryCondition" to batteryCondition
        )
        onConfigurationComplete(config)
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = configItem.title,
            style = MaterialTheme.typography.headlineSmall
        )

        Text("电池条件", style = MaterialTheme.typography.titleMedium)
        Column {
            listOf("高于" to "ABOVE", "低于" to "BELOW", "等于" to "EQUALS").forEach { (label, value) ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = batteryCondition == value,
                        onClick = { batteryCondition = value }
                    )
                    Text(label)
                }
            }
        }

        Text("电池电量: ${batteryLevel}%", style = MaterialTheme.typography.titleMedium)
        Slider(
            value = batteryLevel.toFloat(),
            onValueChange = { batteryLevel = it.toInt() },
            valueRange = 0f..100f,
            modifier = Modifier.fillMaxWidth()
        )
    }
}

/**
 * 通知配置UI
 */
@Composable
private fun NotificationConfigurationUI(
    configItem: ConfigurationCardItem<Any>,
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit
) {
    var notificationTitle by remember { mutableStateOf("") }
    var notificationText by remember { mutableStateOf("") }
    var notificationPriority by remember { mutableStateOf("NORMAL") }

    LaunchedEffect(notificationTitle, notificationText, notificationPriority) {
        if (notificationTitle.isNotBlank() && notificationText.isNotBlank()) {
            val config = mapOf(
                "notificationTitle" to notificationTitle,
                "notificationText" to notificationText,
                "notificationPriority" to notificationPriority
            )
            onConfigurationComplete(config)
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = configItem.title,
            style = MaterialTheme.typography.headlineSmall
        )

        OutlinedTextField(
            value = notificationTitle,
            onValueChange = { notificationTitle = it },
            label = { Text("通知标题") },
            modifier = Modifier.fillMaxWidth()
        )

        OutlinedTextField(
            value = notificationText,
            onValueChange = { notificationText = it },
            label = { Text("通知内容") },
            modifier = Modifier.fillMaxWidth(),
            minLines = 3
        )

        Text("通知优先级", style = MaterialTheme.typography.titleMedium)
        Column {
            listOf("低" to "LOW", "普通" to "NORMAL", "高" to "HIGH").forEach { (label, value) ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = notificationPriority == value,
                        onClick = { notificationPriority = value }
                    )
                    Text(label)
                }
            }
        }
    }
}

/**
 * 默认配置UI
 */
@Composable
private fun DefaultConfigurationUI(
    configItem: ConfigurationCardItem<Any>,
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit
) {
    var configValue by remember { mutableStateOf("") }

    LaunchedEffect(configValue) {
        if (configValue.isNotBlank()) {
            val config = mapOf(
                "itemId" to configItem.id,
                "title" to configItem.title,
                "value" to configValue
            )
            onConfigurationComplete(config)
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = configItem.title,
            style = MaterialTheme.typography.headlineSmall
        )

        Text(
            text = configItem.description,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        OutlinedTextField(
            value = configValue,
            onValueChange = { configValue = it },
            label = { Text("配置值") },
            modifier = Modifier.fillMaxWidth(),
            placeholder = { Text("请输入配置值") }
        )
    }
}

/**
 * 占位符配置内容组件
 */
@Composable
private fun PlaceholderConfigContent(
    title: String,
    onConfigurationComplete: (Any) -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            Text(
                text = "配置内容：$title\n\n这里将显示具体的配置选项。",
                style = MaterialTheme.typography.bodyLarge
            )
        }
    }
}

/**
 * IP地址配置UI
 */
@Composable
private fun IpAddressConfigurationUI(
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit
) {
    var ipAddressCondition by remember { mutableStateOf("IP_ADDRESS_CHANGED") }

    LaunchedEffect(ipAddressCondition) {
        val config = mapOf(
            "ipAddressCondition" to ipAddressCondition
        )
        onConfigurationComplete(config)
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text("IP地址配置", style = MaterialTheme.typography.headlineSmall)

        Text("IP地址条件", style = MaterialTheme.typography.titleMedium)
        Column {
            listOf(
                "IP地址变化时" to "IP_ADDRESS_CHANGED",
                "获得IP地址时" to "IP_ADDRESS_OBTAINED",
                "失去IP地址时" to "IP_ADDRESS_LOST"
            ).forEach { (label, value) ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = ipAddressCondition == value,
                        onClick = { ipAddressCondition = value }
                    )
                    Text(label)
                }
            }
        }
    }
}

// 天空蓝主题专用配置UI组件

/**
 * 天空蓝主题发送短信配置UI
 */
@Composable
private fun SkyBlueSendSmsConfigurationUI(
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit,
    uiSpacingConfig: UISpacingConfigurationManager.UISpacingConfiguration
) {
    var phoneNumber by remember { mutableStateOf("") }
    var messageText by remember { mutableStateOf("") }
    var selectedSimCard by remember { mutableStateOf("ASK_EACH_TIME") }
    var draftOnly by remember { mutableStateOf(false) }

    LaunchedEffect(phoneNumber, messageText, selectedSimCard, draftOnly) {
        if (phoneNumber.isNotBlank() && messageText.isNotBlank()) {
            val config = mapOf(
                "phoneNumber" to phoneNumber,
                "messageText" to messageText,
                "simCardSelection" to selectedSimCard,
                "draftOnly" to draftOnly
            )
            onConfigurationComplete(config)
        }
    }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(0.dp)
    ) {
        // 电话号码输入
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "电话号码",
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurface,
                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                modifier = Modifier.weight(1f)
            )
            OutlinedTextField(
                value = phoneNumber,
                onValueChange = { phoneNumber = it },
                placeholder = { Text("请输入电话号码") },
                modifier = Modifier.weight(2f)
            )
        }

        // 分割线
        if (uiSpacingConfig.dividerVisible) {
            HorizontalDivider(
                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp)
            )
        }

        // 短信内容输入
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
            verticalAlignment = Alignment.Top
        ) {
            Text(
                text = "短信内容",
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurface,
                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                modifier = Modifier.weight(1f).padding(top = 12.dp)
            )
            OutlinedTextField(
                value = messageText,
                onValueChange = { messageText = it },
                placeholder = { Text("请输入短信内容") },
                modifier = Modifier.weight(2f),
                minLines = 3
            )
        }

        // 分割线
        if (uiSpacingConfig.dividerVisible) {
            HorizontalDivider(
                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp)
            )
        }

        // SIM卡选择
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp)
        ) {
            Text(
                text = "SIM卡选择",
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurface,
                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                modifier = Modifier.padding(bottom = 8.dp)
            )

            listOf("SIM1" to "SIM1", "SIM2" to "SIM2", "每次询问" to "ASK_EACH_TIME").forEach { (label, value) ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable { selectedSimCard = value }
                        .padding(vertical = 4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = selectedSimCard == value,
                        onClick = { selectedSimCard = value }
                    )
                    Text(
                        text = label,
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.padding(start = 8.dp)
                    )
                }
            }
        }

        // 分割线
        if (uiSpacingConfig.dividerVisible) {
            HorizontalDivider(
                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp)
            )
        }

        // 仅预填写选项
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { draftOnly = !draftOnly }
                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "仅预填写不发送",
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurface,
                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                modifier = Modifier.weight(1f)
            )
            Checkbox(
                checked = draftOnly,
                onCheckedChange = { draftOnly = it }
            )
        }
    }
}

/**
 * 天空蓝主题通用配置UI
 *
 * 完全通用的、规范化的UI生成逻辑，基于配置字段自动生成配置界面
 * 99%的配置项都可以通过这个通用系统处理
 */
@Composable
private fun SkyBlueGenericConfigurationUI(
    configItem: ConfigurationCardItem<Any>,
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit,
    uiSpacingConfig: UISpacingConfigurationManager.UISpacingConfiguration
) {
    // 使用通用配置字段系统
    SkyBlueUniversalConfigurationUI(
        configItem = configItem,
        initialConfig = initialConfig,
        onConfigurationComplete = onConfigurationComplete,
        uiSpacingConfig = uiSpacingConfig
    )
}

/**
 * 天空蓝主题通用配置UI系统
 *
 * 基于配置字段定义自动生成UI的通用系统
 * 支持所有类型的条件和任务配置
 */
@Composable
private fun SkyBlueUniversalConfigurationUI(
    configItem: ConfigurationCardItem<Any>,
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit,
    uiSpacingConfig: UISpacingConfigurationManager.UISpacingConfiguration
) {
    // 根据配置项类型获取配置字段定义
    val configFields = getConfigurationFields(configItem)

    // 配置状态管理
    val configValues = remember { mutableStateMapOf<String, Any>() }

    // 初始化默认值
    LaunchedEffect(configItem.id) {
        configFields.forEach { field ->
            configValues[field.key] = field.defaultValue
        }
    }

    // 当配置值变化时，触发回调
    LaunchedEffect(configValues.toMap()) {
        if (configValues.isNotEmpty()) {
            val config = configValues.toMap()
            android.util.Log.d("ItemConfigurationActivity", "通用配置UI配置值变化: config=$config")
            onConfigurationComplete(config)
        }
    }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(0.dp)
    ) {
        // 配置标题
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp)
        ) {
            Text(
                text = configItem.title,
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurface,
                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium
            )
            Text(
                text = configItem.description,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(top = 4.dp)
            )
        }

        // 动态生成配置字段UI
        configFields.forEachIndexed { index, field ->
            if (index > 0 && uiSpacingConfig.dividerVisible) {
                HorizontalDivider(
                    modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp)
                )
            }

            when (field.type) {
                ConfigFieldType.SINGLE_CHOICE -> {
                    SkyBlueSingleChoiceField(
                        field = field,
                        currentValue = configValues[field.key] as? String ?: field.defaultValue as String,
                        onValueChange = { configValues[field.key] = it },
                        uiSpacingConfig = uiSpacingConfig
                    )
                }
                ConfigFieldType.TEXT_INPUT -> {
                    SkyBlueTextInputField(
                        field = field,
                        currentValue = configValues[field.key] as? String ?: field.defaultValue as String,
                        onValueChange = { configValues[field.key] = it },
                        uiSpacingConfig = uiSpacingConfig
                    )
                }
                ConfigFieldType.CONDITIONAL_TEXT -> {
                    // 条件文本字段：只有当条件满足时才显示
                    val conditionValue = configValues[field.conditionKey] as? String
                    if (conditionValue == field.conditionValue) {
                        SkyBlueTextInputField(
                            field = field,
                            currentValue = configValues[field.key] as? String ?: field.defaultValue as String,
                            onValueChange = { configValues[field.key] = it },
                            uiSpacingConfig = uiSpacingConfig
                        )
                    }
                }
                ConfigFieldType.CHECKBOX -> {
                    SkyBlueCheckboxField(
                        field = field,
                        currentValue = configValues[field.key] as? Boolean ?: field.defaultValue as Boolean,
                        onValueChange = { configValues[field.key] = it },
                        uiSpacingConfig = uiSpacingConfig
                    )
                }
                ConfigFieldType.SLIDER -> {
                    SkyBlueSliderField(
                        field = field,
                        currentValue = configValues[field.key] as? Int ?: field.defaultValue as Int,
                        onValueChange = { configValues[field.key] = it },
                        uiSpacingConfig = uiSpacingConfig
                    )
                }
                ConfigFieldType.SLIDER -> {
                    SkyBlueSliderField(
                        field = field,
                        currentValue = configValues[field.key] as? Int ?: field.defaultValue as Int,
                        onValueChange = { configValues[field.key] = it },
                        uiSpacingConfig = uiSpacingConfig
                    )
                }
                ConfigFieldType.CONDITIONAL_TEXT -> {
                    SkyBlueConditionalTextField(
                        field = field,
                        currentValue = configValues[field.key] as? String ?: field.defaultValue as String,
                        onValueChange = { configValues[field.key] = it },
                        conditionKey = field.conditionKey!!,
                        conditionValue = field.conditionValue!!,
                        configValues = configValues,
                        uiSpacingConfig = uiSpacingConfig
                    )
                }
            }
        }
    }
}

/**
 * 天空蓝主题通信状态配置UI
 *
 * 通用的通信状态配置界面，支持短信和通话相关的所有配置
 */
@Composable
private fun SkyBlueCommunicationStateConfigurationUI(
    configItem: ConfigurationCardItem<Any>,
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit,
    uiSpacingConfig: UISpacingConfigurationManager.UISpacingConfiguration
) {
    var filterType by remember { mutableStateOf("ANY_CONTACT") }
    var specificNumber by remember { mutableStateOf("") }
    var filterMode by remember { mutableStateOf("INCLUDE") }

    LaunchedEffect(filterType, specificNumber, filterMode) {
        val config = mapOf(
            "stateType" to configItem.operationType.toString(),
            "filterType" to filterType,
            "specificNumber" to specificNumber,
            "filterMode" to filterMode
        )
        onConfigurationComplete(config)
    }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(0.dp)
    ) {
        // 配置标题
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp)
        ) {
            Text(
                text = configItem.title,
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurface,
                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium
            )
            Text(
                text = "配置${configItem.title}的触发条件",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(top = 4.dp)
            )
        }

        // 分割线
        if (uiSpacingConfig.dividerVisible) {
            HorizontalDivider(
                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp)
            )
        }

        // 联系人筛选类型
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp)
        ) {
            Text(
                text = "联系人筛选类型",
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurface,
                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                modifier = Modifier.padding(bottom = 8.dp)
            )

            listOf(
                "任何联系人" to "ANY_CONTACT",
                "陌生号码" to "NO_CONTACT",
                "指定号码" to "SPECIFIC_NUMBER"
            ).forEach { (label, value) ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable { filterType = value }
                        .padding(vertical = 4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = filterType == value,
                        onClick = { filterType = value }
                    )
                    Text(
                        text = label,
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.padding(start = 8.dp)
                    )
                }
            }
        }

        // 指定号码输入
        if (filterType == "SPECIFIC_NUMBER") {
            // 分割线
            if (uiSpacingConfig.dividerVisible) {
                HorizontalDivider(
                    modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp)
                )
            }

            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "指定号码",
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.onSurface,
                    fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                    modifier = Modifier.weight(1f)
                )
                OutlinedTextField(
                    value = specificNumber,
                    onValueChange = { specificNumber = it },
                    placeholder = { Text("请输入电话号码") },
                    modifier = Modifier.weight(2f)
                )
            }
        }

        // 筛选模式（仅在有具体筛选时显示）
        if (filterType != "ANY_CONTACT") {
            // 分割线
            if (uiSpacingConfig.dividerVisible) {
                HorizontalDivider(
                    modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp)
                )
            }

            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp)
            ) {
                Text(
                    text = "筛选模式",
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.onSurface,
                    fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                    modifier = Modifier.padding(bottom = 8.dp)
                )

                listOf("包含" to "INCLUDE", "排除" to "EXCLUDE").forEach { (label, value) ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { filterMode = value }
                            .padding(vertical = 4.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = filterMode == value,
                            onClick = { filterMode = value }
                        )
                        Text(
                            text = label,
                            style = MaterialTheme.typography.bodyMedium,
                            modifier = Modifier.padding(start = 8.dp)
                        )
                    }
                }
            }
        }
    }
}

/**
 * 天空蓝主题连接状态配置UI
 *
 * 通用的连接状态配置界面，支持WiFi、蓝牙、移动数据、NFC等连接状态配置
 */
@Composable
private fun SkyBlueConnectionStateConfigurationUI(
    configItem: ConfigurationCardItem<Any>,
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit,
    uiSpacingConfig: UISpacingConfigurationManager.UISpacingConfiguration
) {
    var connectionState by remember { mutableStateOf("CONNECTED") }
    var networkName by remember { mutableStateOf("") }
    var requireSpecificNetwork by remember { mutableStateOf(false) }

    // 根据操作类型确定连接类型名称
    val operationTypeString = configItem.operationType.toString()
    val connectionTypeName = when {
        operationTypeString.contains("WIFI") -> "WiFi"
        operationTypeString.contains("BLUETOOTH") -> "蓝牙"
        operationTypeString.contains("MOBILE_DATA") -> "移动数据"
        operationTypeString.contains("NFC") -> "NFC"
        else -> "连接"
    }

    LaunchedEffect(connectionState, networkName, requireSpecificNetwork) {
        val config = mapOf(
            "connectionType" to operationTypeString,
            "connectionState" to connectionState,
            "networkName" to networkName,
            "requireSpecificNetwork" to requireSpecificNetwork
        )
        onConfigurationComplete(config)
    }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(0.dp)
    ) {
        // 配置标题
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp)
        ) {
            Text(
                text = configItem.title,
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurface,
                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium
            )
            Text(
                text = "配置${connectionTypeName}状态的触发条件",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(top = 4.dp)
            )
        }

        // 分割线
        if (uiSpacingConfig.dividerVisible) {
            HorizontalDivider(
                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp)
            )
        }

        // 连接状态选择
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp)
        ) {
            Text(
                text = "${connectionTypeName}状态",
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurface,
                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                modifier = Modifier.padding(bottom = 8.dp)
            )

            listOf("已连接" to "CONNECTED", "已断开" to "DISCONNECTED", "正在连接" to "CONNECTING").forEach { (label, value) ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable { connectionState = value }
                        .padding(vertical = 4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = connectionState == value,
                        onClick = { connectionState = value }
                    )
                    Text(
                        text = label,
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.padding(start = 8.dp)
                    )
                }
            }
        }

        // 对于WiFi和蓝牙，显示指定网络/设备选项
        if (operationTypeString.contains("WIFI") || operationTypeString.contains("BLUETOOTH")) {
            // 分割线
            if (uiSpacingConfig.dividerVisible) {
                HorizontalDivider(
                    modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp)
                )
            }

            // 指定网络/设备选项
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { requireSpecificNetwork = !requireSpecificNetwork }
                    .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = if (operationTypeString.contains("WIFI")) "指定网络" else "指定设备",
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.onSurface,
                    fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                    modifier = Modifier.weight(1f)
                )
                Checkbox(
                    checked = requireSpecificNetwork,
                    onCheckedChange = { requireSpecificNetwork = it }
                )
            }

            // 网络名称/设备名称输入
            if (requireSpecificNetwork) {
                // 分割线
                if (uiSpacingConfig.dividerVisible) {
                    HorizontalDivider(
                        modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp)
                    )
                }

                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = if (operationTypeString.contains("WIFI")) "网络名称" else "设备名称",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurface,
                        fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                        modifier = Modifier.weight(1f)
                    )
                    OutlinedTextField(
                        value = networkName,
                        onValueChange = { networkName = it },
                        placeholder = {
                            Text(if (operationTypeString.contains("WIFI")) "请输入WiFi网络名称" else "请输入蓝牙设备名称")
                        },
                        modifier = Modifier.weight(2f)
                    )
                }
            }
        }
    }
}

/**
 * 天空蓝主题电池状态配置UI
 */
@Composable
private fun SkyBlueBatteryStateConfigurationUI(
    configItem: ConfigurationCardItem<Any>,
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit,
    uiSpacingConfig: UISpacingConfigurationManager.UISpacingConfiguration
) {
    var batteryLevel by remember { mutableStateOf(50) }
    var batteryCondition by remember { mutableStateOf("ABOVE") }

    LaunchedEffect(batteryLevel, batteryCondition) {
        val config = mapOf(
            "batteryLevel" to batteryLevel,
            "batteryCondition" to batteryCondition
        )
        onConfigurationComplete(config)
    }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(0.dp)
    ) {
        // 配置标题
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp)
        ) {
            Text(
                text = configItem.title,
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurface,
                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium
            )
            Text(
                text = "配置电池状态的触发条件",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(top = 4.dp)
            )
        }

        // 分割线
        if (uiSpacingConfig.dividerVisible) {
            HorizontalDivider(
                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp)
            )
        }

        // 电池条件选择
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp)
        ) {
            Text(
                text = "电池条件",
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurface,
                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                modifier = Modifier.padding(bottom = 8.dp)
            )

            listOf("高于" to "ABOVE", "低于" to "BELOW", "等于" to "EQUALS").forEach { (label, value) ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable { batteryCondition = value }
                        .padding(vertical = 4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = batteryCondition == value,
                        onClick = { batteryCondition = value }
                    )
                    Text(
                        text = label,
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.padding(start = 8.dp)
                    )
                }
            }
        }

        // 分割线
        if (uiSpacingConfig.dividerVisible) {
            HorizontalDivider(
                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp)
            )
        }

        // 电池电量滑块
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp)
        ) {
            Text(
                text = "电池电量: ${batteryLevel}%",
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurface,
                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                modifier = Modifier.padding(bottom = 8.dp)
            )
            Slider(
                value = batteryLevel.toFloat(),
                onValueChange = { batteryLevel = it.toInt() },
                valueRange = 0f..100f,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

// 其他配置UI组件的占位符实现
@Composable
private fun SkyBlueSensorStateConfigurationUI(
    configItem: ConfigurationCardItem<Any>,
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit,
    uiSpacingConfig: UISpacingConfigurationManager.UISpacingConfiguration
) {
    SkyBlueDefaultConfigurationUI(configItem, initialConfig, onConfigurationComplete, uiSpacingConfig)
}

@Composable
private fun SkyBlueAppStateConfigurationUI(
    configItem: ConfigurationCardItem<Any>,
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit,
    uiSpacingConfig: UISpacingConfigurationManager.UISpacingConfiguration
) {
    SkyBlueDefaultConfigurationUI(configItem, initialConfig, onConfigurationComplete, uiSpacingConfig)
}

@Composable
private fun SkyBlueDeviceEventConfigurationUI(
    configItem: ConfigurationCardItem<Any>,
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit,
    uiSpacingConfig: UISpacingConfigurationManager.UISpacingConfiguration
) {
    SkyBlueDefaultConfigurationUI(configItem, initialConfig, onConfigurationComplete, uiSpacingConfig)
}

@Composable
private fun SkyBlueInformationTaskConfigurationUI(
    configItem: ConfigurationCardItem<Any>,
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit,
    uiSpacingConfig: UISpacingConfigurationManager.UISpacingConfiguration
) {
    // 对于发送短信等信息任务，使用专门的发送短信配置UI
    SkyBlueSendSmsConfigurationUI(initialConfig, onConfigurationComplete, uiSpacingConfig)
}

@Composable
private fun SkyBluePhoneTaskConfigurationUI(
    configItem: ConfigurationCardItem<Any>,
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit,
    uiSpacingConfig: UISpacingConfigurationManager.UISpacingConfiguration
) {
    SkyBlueDefaultConfigurationUI(configItem, initialConfig, onConfigurationComplete, uiSpacingConfig)
}

@Composable
private fun SkyBlueNotificationTaskConfigurationUI(
    configItem: ConfigurationCardItem<Any>,
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit,
    uiSpacingConfig: UISpacingConfigurationManager.UISpacingConfiguration
) {
    SkyBlueDefaultConfigurationUI(configItem, initialConfig, onConfigurationComplete, uiSpacingConfig)
}

@Composable
private fun SkyBlueConnectivityTaskConfigurationUI(
    configItem: ConfigurationCardItem<Any>,
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit,
    uiSpacingConfig: UISpacingConfigurationManager.UISpacingConfiguration
) {
    SkyBlueDefaultConfigurationUI(configItem, initialConfig, onConfigurationComplete, uiSpacingConfig)
}

@Composable
private fun SkyBlueDeviceSettingsTaskConfigurationUI(
    configItem: ConfigurationCardItem<Any>,
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit,
    uiSpacingConfig: UISpacingConfigurationManager.UISpacingConfiguration
) {
    SkyBlueDefaultConfigurationUI(configItem, initialConfig, onConfigurationComplete, uiSpacingConfig)
}

/**
 * 配置字段类型枚举
 */
enum class ConfigFieldType {
    SINGLE_CHOICE,      // 单选
    TEXT_INPUT,         // 文本输入
    CHECKBOX,           // 复选框
    SLIDER,             // 滑块
    CONDITIONAL_TEXT    // 条件文本输入
}

/**
 * 配置字段定义
 */
data class ConfigField(
    val key: String,                    // 字段键
    val label: String,                  // 字段标签
    val type: ConfigFieldType,          // 字段类型
    val defaultValue: Any,              // 默认值
    val options: List<Pair<String, String>>? = null,  // 选项列表（用于单选）
    val placeholder: String? = null,    // 占位符（用于文本输入）
    val range: IntRange? = null,        // 范围（用于滑块）
    val unit: String? = null,           // 单位（用于滑块）
    val conditionKey: String? = null,   // 条件字段键（用于条件文本）
    val conditionValue: String? = null  // 条件值（用于条件文本）
)

/**
 * 根据配置项获取配置字段定义
 *
 * 完全通用的配置字段生成系统，基于配置项的内容组件自动推断字段类型
 * 不依赖具体的操作类型，而是通过分析配置项的content组件来生成通用字段
 */
private fun getConfigurationFields(configItem: ConfigurationCardItem<Any>): List<ConfigField> {
    // 通用配置字段生成逻辑
    // 根据配置项ID和标题推断可能的配置字段

    return when {
        // 状态选择类配置（大多数条件都是状态选择）
        configItem.id.contains("state") ||
        configItem.id.contains("sensor") ||
        configItem.id.contains("event") -> {
            listOf(
                ConfigField(
                    key = "primaryState",
                    label = "状态选择",
                    type = ConfigFieldType.SINGLE_CHOICE,
                    defaultValue = "ENABLED",
                    options = listOf(
                        "启用" to "ENABLED",
                        "禁用" to "DISABLED",
                        "开启" to "ON",
                        "关闭" to "OFF"
                    )
                ),
                ConfigField(
                    key = "threshold",
                    label = "阈值设置",
                    type = ConfigFieldType.TEXT_INPUT,
                    defaultValue = "",
                    placeholder = "请输入阈值（可选）"
                ),
                ConfigField(
                    key = "enableAdvanced",
                    label = "启用高级选项",
                    type = ConfigFieldType.CHECKBOX,
                    defaultValue = false
                )
            )
        }

        // 应用相关配置
        configItem.id.contains("app") -> {
            listOf(
                ConfigField(
                    key = "targetApp",
                    label = "目标应用",
                    type = ConfigFieldType.TEXT_INPUT,
                    defaultValue = "",
                    placeholder = "请输入应用包名或留空表示任意应用"
                ),
                ConfigField(
                    key = "actionType",
                    label = "操作类型",
                    type = ConfigFieldType.SINGLE_CHOICE,
                    defaultValue = "LAUNCH",
                    options = listOf(
                        "启动" to "LAUNCH",
                        "关闭" to "CLOSE",
                        "切换" to "SWITCH"
                    )
                )
            )
        }

        // 通信相关配置（短信、通话等）
        configItem.id.contains("sms") ||
        configItem.id.contains("call") ||
        configItem.id.contains("email") -> {
            listOf(
                ConfigField(
                    key = "contactFilter",
                    label = "联系人筛选",
                    type = ConfigFieldType.SINGLE_CHOICE,
                    defaultValue = "ANY",
                    options = listOf(
                        "任何联系人" to "ANY",
                        "指定联系人" to "SPECIFIC",
                        "陌生号码" to "UNKNOWN"
                    )
                ),
                ConfigField(
                    key = "contactInfo",
                    label = "联系人信息",
                    type = ConfigFieldType.CONDITIONAL_TEXT,
                    defaultValue = "",
                    placeholder = "请输入号码或邮箱",
                    conditionKey = "contactFilter",
                    conditionValue = "SPECIFIC"
                ),
                ConfigField(
                    key = "messageContent",
                    label = "消息内容",
                    type = ConfigFieldType.TEXT_INPUT,
                    defaultValue = "",
                    placeholder = "请输入消息内容（可选）"
                )
            )
        }

        // 连接相关配置（WiFi、蓝牙等）
        configItem.id.contains("wifi") ||
        configItem.id.contains("bluetooth") ||
        configItem.id.contains("network") -> {
            listOf(
                ConfigField(
                    key = "connectionState",
                    label = "连接状态",
                    type = ConfigFieldType.SINGLE_CHOICE,
                    defaultValue = "CONNECTED",
                    options = listOf(
                        "已连接" to "CONNECTED",
                        "已断开" to "DISCONNECTED",
                        "正在连接" to "CONNECTING"
                    )
                ),
                ConfigField(
                    key = "specificTarget",
                    label = "指定目标",
                    type = ConfigFieldType.CHECKBOX,
                    defaultValue = false
                ),
                ConfigField(
                    key = "targetName",
                    label = "目标名称",
                    type = ConfigFieldType.CONDITIONAL_TEXT,
                    defaultValue = "",
                    placeholder = "请输入网络或设备名称",
                    conditionKey = "specificTarget",
                    conditionValue = "true"
                )
            )
        }

        // 电池相关配置
        configItem.id.contains("battery") -> {
            listOf(
                ConfigField(
                    key = "batteryCondition",
                    label = "电池条件",
                    type = ConfigFieldType.SINGLE_CHOICE,
                    defaultValue = "ABOVE",
                    options = listOf(
                        "高于" to "ABOVE",
                        "低于" to "BELOW",
                        "等于" to "EQUALS"
                    )
                ),
                ConfigField(
                    key = "batteryLevel",
                    label = "电池电量",
                    type = ConfigFieldType.SLIDER,
                    defaultValue = 50,
                    range = 0..100,
                    unit = "%"
                )
            )
        }

        // 默认通用配置（适用于所有未明确分类的配置项）
        else -> {
            listOf(
                ConfigField(
                    key = "primaryOption",
                    label = "主要选项",
                    type = ConfigFieldType.SINGLE_CHOICE,
                    defaultValue = "OPTION_1",
                    options = listOf(
                        "选项1" to "OPTION_1",
                        "选项2" to "OPTION_2",
                        "选项3" to "OPTION_3"
                    )
                ),
                ConfigField(
                    key = "customValue",
                    label = "自定义值",
                    type = ConfigFieldType.TEXT_INPUT,
                    defaultValue = "",
                    placeholder = "请输入自定义值（可选）"
                ),
                ConfigField(
                    key = "enableFeature",
                    label = "启用功能",
                    type = ConfigFieldType.CHECKBOX,
                    defaultValue = true
                )
            )
        }
    }
}

/**
 * 天空蓝主题单选字段组件
 */
@Composable
private fun SkyBlueSingleChoiceField(
    field: ConfigField,
    currentValue: String,
    onValueChange: (String) -> Unit,
    uiSpacingConfig: UISpacingConfigurationManager.UISpacingConfiguration
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp)
    ) {
        Text(
            text = field.label,
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.onSurface,
            fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
            modifier = Modifier.padding(bottom = 8.dp)
        )

        field.options?.forEach { (label, value) ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { onValueChange(value) }
                    .padding(vertical = 4.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = currentValue == value,
                    onClick = { onValueChange(value) }
                )
                Text(
                    text = label,
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
        }
    }
}

/**
 * 天空蓝主题文本输入字段组件
 */
@Composable
private fun SkyBlueTextInputField(
    field: ConfigField,
    currentValue: String,
    onValueChange: (String) -> Unit,
    uiSpacingConfig: UISpacingConfigurationManager.UISpacingConfiguration
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = field.label,
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.onSurface,
            fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
            modifier = Modifier.weight(1f)
        )
        OutlinedTextField(
            value = currentValue,
            onValueChange = onValueChange,
            placeholder = field.placeholder?.let { { Text(it) } },
            modifier = Modifier.weight(2f)
        )
    }
}

/**
 * 天空蓝主题复选框字段组件
 */
@Composable
private fun SkyBlueCheckboxField(
    field: ConfigField,
    currentValue: Boolean,
    onValueChange: (Boolean) -> Unit,
    uiSpacingConfig: UISpacingConfigurationManager.UISpacingConfiguration
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onValueChange(!currentValue) }
            .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = field.label,
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.onSurface,
            fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
            modifier = Modifier.weight(1f)
        )
        Checkbox(
            checked = currentValue,
            onCheckedChange = onValueChange
        )
    }
}

/**
 * 天空蓝主题滑块字段组件
 */
@Composable
private fun SkyBlueSliderField(
    field: ConfigField,
    currentValue: Int,
    onValueChange: (Int) -> Unit,
    uiSpacingConfig: UISpacingConfigurationManager.UISpacingConfiguration
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp)
    ) {
        Text(
            text = "${field.label}: $currentValue${field.unit ?: ""}",
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.onSurface,
            fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        Slider(
            value = currentValue.toFloat(),
            onValueChange = { onValueChange(it.toInt()) },
            valueRange = field.range?.let { it.first.toFloat()..it.last.toFloat() } ?: 0f..100f,
            modifier = Modifier.fillMaxWidth()
        )
    }
}

/**
 * 天空蓝主题条件文本字段组件
 */
@Composable
private fun SkyBlueConditionalTextField(
    field: ConfigField,
    currentValue: String,
    onValueChange: (String) -> Unit,
    conditionKey: String,
    conditionValue: String,
    configValues: Map<String, Any>,
    uiSpacingConfig: UISpacingConfigurationManager.UISpacingConfiguration
) {
    // 检查条件是否满足
    val conditionMet = when (val conditionVal = configValues[conditionKey]) {
        is Boolean -> conditionVal.toString() == conditionValue
        is String -> conditionVal == conditionValue
        else -> false
    }

    if (conditionMet) {
        SkyBlueTextInputField(field, currentValue, onValueChange, uiSpacingConfig)
    }
}

/**
 * 天空蓝主题占位符配置内容组件
 */
@Composable
private fun SkyBluePlaceholderConfigContent(
    title: String,
    onConfigurationComplete: (Any) -> Unit,
    uiSpacingConfig: UISpacingConfigurationManager.UISpacingConfiguration
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp)
    ) {
        Text(
            text = title,
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.onSurface,
            fontWeight = androidx.compose.ui.text.font.FontWeight.Medium
        )
        Text(
            text = "配置内容正在开发中...",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(top = 4.dp)
        )
    }
}

/**
 * 天空蓝主题默认配置UI
 */
@Composable
private fun SkyBlueDefaultConfigurationUI(
    configItem: ConfigurationCardItem<Any>,
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit,
    uiSpacingConfig: UISpacingConfigurationManager.UISpacingConfiguration
) {
    // 自动生成默认配置
    LaunchedEffect(Unit) {
        val config = mapOf(
            "itemId" to configItem.id,
            "title" to configItem.title,
            "operationType" to configItem.operationType.toString(),
            "configured" to true
        )
        onConfigurationComplete(config)
    }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(0.dp)
    ) {
        // 配置项标题和描述
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp)
        ) {
            Text(
                text = configItem.title,
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurface,
                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium
            )
            Text(
                text = configItem.description,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(top = 4.dp)
            )
        }

        // 分割线
        if (uiSpacingConfig.dividerVisible) {
            HorizontalDivider(
                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp)
            )
        }

        // 显示配置状态
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "配置状态",
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurface,
                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                modifier = Modifier.weight(1f)
            )
            Text(
                text = "已自动配置",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.primary
            )
        }
    }
}

// 其他天空蓝主题配置UI组件的占位符实现
@Composable
private fun SkyBlueSendEmailConfigurationUI(
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit,
    uiSpacingConfig: UISpacingConfigurationManager.UISpacingConfiguration
) {
    SkyBluePlaceholderConfigContent("发送邮件配置", onConfigurationComplete, uiSpacingConfig)
}

@Composable
private fun SkyBlueOpenCallLogConfigurationUI(
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit,
    uiSpacingConfig: UISpacingConfigurationManager.UISpacingConfiguration
) {
    SkyBluePlaceholderConfigContent("打开通话记录配置", onConfigurationComplete, uiSpacingConfig)
}

@Composable
private fun SkyBlueWifiStateConfigurationUI(
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit,
    uiSpacingConfig: UISpacingConfigurationManager.UISpacingConfiguration
) {
    SkyBluePlaceholderConfigContent("WiFi状态配置", onConfigurationComplete, uiSpacingConfig)
}

@Composable
private fun SkyBlueIpAddressConfigurationUI(
    initialConfig: String?,
    onConfigurationComplete: (Any) -> Unit,
    uiSpacingConfig: UISpacingConfigurationManager.UISpacingConfiguration
) {
    SkyBluePlaceholderConfigContent("IP地址配置", onConfigurationComplete, uiSpacingConfig)
}


