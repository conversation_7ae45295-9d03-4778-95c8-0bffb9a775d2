package com.weinuo.quickcommands.ui.activities

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.ui.unit.dp
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import com.weinuo.quickcommands.R
import com.weinuo.quickcommands.ui.theme.QuickCommandsTheme
import com.weinuo.quickcommands.ui.configuration.ConnectionStateConfigProvider
import com.weinuo.quickcommands.ui.configuration.CommunicationStateConfigProvider
import com.weinuo.quickcommands.ui.configuration.PhoneTaskConfigProvider
import com.weinuo.quickcommands.ui.components.ConfigurationCardItem

/**
 * 单个配置项配置Activity
 *
 * 用于显示单个配置项的详细配置界面，替代原来的卡片展开方式。
 * 支持从ConfigurationCardItem加载配置内容。
 */
class ItemConfigurationActivity : ComponentActivity() {
    
    companion object {
        private const val EXTRA_ITEM_ID = "item_id"
        private const val EXTRA_ITEM_TITLE = "item_title"
        private const val EXTRA_INITIAL_CONFIG = "initial_config"
        private const val EXTRA_EDIT_INDEX = "edit_index"
        
        /**
         * 启动单个配置项配置Activity
         */
        fun startForConfiguration(
            context: Context,
            itemId: String,
            itemTitle: String,
            initialConfig: Any? = null,
            editIndex: Int? = null
        ) {
            val intent = Intent(context, ItemConfigurationActivity::class.java).apply {
                putExtra(EXTRA_ITEM_ID, itemId)
                putExtra(EXTRA_ITEM_TITLE, itemTitle)
                initialConfig?.let { putExtra(EXTRA_INITIAL_CONFIG, it.toString()) }
                editIndex?.let { putExtra(EXTRA_EDIT_INDEX, it) }
            }
            context.startActivity(intent)
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 获取传递的参数
        val itemId = intent.getStringExtra(EXTRA_ITEM_ID) ?: ""
        val itemTitle = intent.getStringExtra(EXTRA_ITEM_TITLE) ?: ""
        val initialConfig = intent.getStringExtra(EXTRA_INITIAL_CONFIG)
        val editIndex = intent.getIntExtra(EXTRA_EDIT_INDEX, -1).takeIf { it != -1 }
        
        setContent {
            QuickCommandsTheme {
                ItemConfigurationActivityContent(
                    itemId = itemId,
                    itemTitle = itemTitle,
                    initialConfig = initialConfig,
                    editIndex = editIndex,
                    onFinish = { finish() },
                    onConfigurationComplete = { result ->
                        // 配置完成，返回结果
                        val resultIntent = Intent().apply {
                            putExtra("configuration_result", result.toString())
                            putExtra("item_id", itemId)
                            editIndex?.let { putExtra("edit_index", it) }
                        }
                        setResult(Activity.RESULT_OK, resultIntent)
                        finish()
                    }
                )
            }
        }
    }
}

/**
 * 单个配置项配置Activity的内容组件
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ItemConfigurationActivityContent(
    itemId: String,
    itemTitle: String,
    initialConfig: String?,
    editIndex: Int?,
    onFinish: () -> Unit,
    onConfigurationComplete: (Any) -> Unit
) {
    val context = LocalContext.current
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(itemTitle) },
                navigationIcon = {
                    IconButton(onClick = onFinish) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = stringResource(R.string.back)
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // 根据itemId加载对应的配置内容组件
            ItemConfigurationContent(
                itemId = itemId,
                initialConfig = initialConfig,
                editIndex = editIndex,
                onConfigurationComplete = onConfigurationComplete
            )
        }
    }
}

/**
 * 单个配置项内容组件
 *
 * 根据itemId动态加载对应的配置内容组件。
 */
@Composable
private fun ItemConfigurationContent(
    itemId: String,
    initialConfig: String?,
    editIndex: Int?,
    onConfigurationComplete: (Any) -> Unit
) {
    val context = LocalContext.current

    // 根据itemId查找对应的ConfigurationCardItem并显示其内容
    val configItem = remember(itemId) {
        findConfigurationCardItem(context, itemId)
    }

    if (configItem != null) {
        // 找到了对应的配置项，显示其内容
        if (initialConfig != null && configItem.editableContent != null) {
            // 编辑模式：使用支持初始配置对象的内容组件
            configItem.editableContent!!(configItem.operationType, initialConfig) { result: Any ->
                onConfigurationComplete(result)
            }
        } else {
            // 新建模式：使用普通的内容组件
            configItem.content(configItem.operationType) { result: Any ->
                onConfigurationComplete(result)
            }
        }
    } else {
        // 没有找到对应的配置项，显示占位内容
        PlaceholderConfigContent("未知配置项: $itemId", onConfigurationComplete)
    }
}

/**
 * 根据itemId查找对应的ConfigurationCardItem
 */
private fun findConfigurationCardItem(context: Context, itemId: String): ConfigurationCardItem<*>? {
    // 从各个配置提供器中查找对应的配置项
    val allProviders = listOf(
        { ConnectionStateConfigProvider.getConfigurationItems(context) },
        { CommunicationStateConfigProvider.getConfigurationItems(context) },
        { PhoneTaskConfigProvider.getConfigurationItems(context) }
        // 可以添加更多配置提供器
    )

    for (provider in allProviders) {
        val items = provider()
        val item = items.find { it.id == itemId }
        if (item != null) {
            return item
        }
    }

    return null
}

/**
 * 占位符配置内容组件
 */
@Composable
private fun PlaceholderConfigContent(
    title: String,
    onConfigurationComplete: (Any) -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            Text(
                text = "配置内容：$title\n\n这里将显示具体的配置选项。",
                style = MaterialTheme.typography.bodyLarge
            )
        }
    }
}
