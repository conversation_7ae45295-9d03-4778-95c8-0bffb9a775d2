package com.weinuo.quickcommands.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import com.weinuo.quickcommands.R
import com.weinuo.quickcommands.ui.configuration.*

/**
 * 具体配置项界面
 *
 * 用于配置单个配置项的详细参数，替代原来的卡片展开方式。
 * 根据itemId加载对应的配置内容组件。
 *
 * @param itemId 配置项ID
 * @param editData 编辑数据（可选）
 * @param editIndex 编辑索引（可选）
 * @param onConfigurationComplete 配置完成回调
 * @param onDismiss 取消回调
 * @param navController 导航控制器
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ItemConfigurationScreen(
    itemId: String,
    editData: String? = null,
    editIndex: Int? = null,
    onConfigurationComplete: (Any) -> Unit,
    onDismiss: () -> Unit,
    navController: NavController
) {
    val context = LocalContext.current

    // 根据itemId获取配置标题
    val title = remember(itemId) {
        getConfigurationTitle(context, itemId)
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(title) },
                navigationIcon = {
                    IconButton(onClick = onDismiss) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = stringResource(R.string.back)
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // 根据itemId加载对应的配置内容组件
            ItemConfigurationContent(
                itemId = itemId,
                editData = editData,
                editIndex = editIndex,
                onConfigurationComplete = onConfigurationComplete,
                navController = navController
            )
        }
    }
}

/**
 * 具体配置项内容组件
 *
 * 根据itemId动态加载对应的配置内容组件。
 */
@Composable
private fun ItemConfigurationContent(
    itemId: String,
    editData: String? = null,
    editIndex: Int? = null,
    onConfigurationComplete: (Any) -> Unit,
    navController: NavController
) {
    // 根据itemId加载对应的配置内容
    when (itemId) {
        // 通信状态条件的子项
        "sms_received" -> {
            // 这里应该加载SMS接收配置组件
            // 暂时显示占位内容
            PlaceholderConfigContent("SMS接收配置", onConfigurationComplete)
        }
        "call_received" -> {
            PlaceholderConfigContent("来电配置", onConfigurationComplete)
        }
        
        // 电话任务的子项
        "make_call" -> {
            PlaceholderConfigContent("拨打电话配置", onConfigurationComplete)
        }
        "open_call_log" -> {
            PlaceholderConfigContent("打开通话记录配置", onConfigurationComplete)
        }
        
        // 其他配置项...
        else -> {
            PlaceholderConfigContent("未知配置项: $itemId", onConfigurationComplete)
        }
    }
}

/**
 * 占位配置内容组件
 *
 * 用于演示配置界面的基本结构。
 */
@Composable
private fun PlaceholderConfigContent(
    title: String,
    onConfigurationComplete: (Any) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = title,
            style = MaterialTheme.typography.headlineSmall
        )
        
        Text(
            text = "这里是配置内容区域。在实际实现中，这里会显示具体的配置选项。",
            style = MaterialTheme.typography.bodyMedium
        )
        
        Spacer(modifier = Modifier.weight(1f))
        
        Button(
            onClick = {
                // 模拟配置完成
                onConfigurationComplete("配置结果")
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("完成配置")
        }
    }
}

/**
 * 根据配置项ID获取标题
 */
private fun getConfigurationTitle(context: android.content.Context, itemId: String): String {
    return when (itemId) {
        "sms_received" -> "SMS接收配置"
        "call_received" -> "来电配置"
        "make_call" -> "拨打电话配置"
        "open_call_log" -> "打开通话记录配置"
        else -> "配置项"
    }
}
